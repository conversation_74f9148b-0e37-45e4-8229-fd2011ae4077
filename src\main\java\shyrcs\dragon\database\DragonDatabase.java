package shyrcs.dragon.database;

import org.bukkit.Location;
import shyrcs.dragon.SoulDragonPlugin;

import java.io.File;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * SQLite database for storing Dragon Battle setup locations
 */
public class DragonDatabase {
    
    private final File databaseFile;
    private Connection connection;
    
    public DragonDatabase(File dataFolder) {
        this.databaseFile = new File(dataFolder, "dragon_setup.db");
        initializeDatabase();
    }
    
    /**
     * Initialize database and create tables
     */
    private void initializeDatabase() {
        try {
            // Create data folder if it doesn't exist
            if (!databaseFile.getParentFile().exists()) {
                databaseFile.getParentFile().mkdirs();
            }
            
            // Connect to SQLite
            String url = "jdbc:sqlite:" + databaseFile.getAbsolutePath();
            connection = DriverManager.getConnection(url);
            
            // Create tables
            createTables();
            
            SoulDragonPlugin.info("Database initialized successfully");
            
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to initialize database: " + e.getMessage());
        }
    }
    
    /**
     * Create all required tables
     */
    private void createTables() throws SQLException {
        // Boss spawn positions table
        String createBossPositions = """
            CREATE TABLE IF NOT EXISTS boss_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                world TEXT NOT NULL,
                x DOUBLE NOT NULL,
                y DOUBLE NOT NULL,
                z DOUBLE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """;
        
        // Altar positions table
        String createAltarPositions = """
            CREATE TABLE IF NOT EXISTS altar_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                world TEXT NOT NULL,
                x DOUBLE NOT NULL,
                y DOUBLE NOT NULL,
                z DOUBLE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """;
        
        // End Crystal positions table
        String createCrystalPositions = """
            CREATE TABLE IF NOT EXISTS crystal_positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                world TEXT NOT NULL,
                x DOUBLE NOT NULL,
                y DOUBLE NOT NULL,
                z DOUBLE NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """;
        
        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createBossPositions);
            stmt.execute(createAltarPositions);
            stmt.execute(createCrystalPositions);
        }
    }
    
    /**
     * Save boss spawn position
     */
    public boolean saveBossPosition(Location location) {
        String sql = "INSERT INTO boss_positions (world, x, y, z) VALUES (?, ?, ?, ?)";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, location.getWorld().getName());
            pstmt.setDouble(2, location.getX());
            pstmt.setDouble(3, location.getY());
            pstmt.setDouble(4, location.getZ());
            
            int affected = pstmt.executeUpdate();
            return affected > 0;
            
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to save boss position: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Save altar position
     */
    public boolean saveAltarPosition(Location location) {
        String sql = "INSERT INTO altar_positions (world, x, y, z) VALUES (?, ?, ?, ?)";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, location.getWorld().getName());
            pstmt.setDouble(2, location.getX());
            pstmt.setDouble(3, location.getY());
            pstmt.setDouble(4, location.getZ());
            
            int affected = pstmt.executeUpdate();
            return affected > 0;
            
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to save altar position: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Save crystal position
     */
    public boolean saveCrystalPosition(Location location) {
        String sql = "INSERT INTO crystal_positions (world, x, y, z) VALUES (?, ?, ?, ?)";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, location.getWorld().getName());
            pstmt.setDouble(2, location.getX());
            pstmt.setDouble(3, location.getY());
            pstmt.setDouble(4, location.getZ());
            
            int affected = pstmt.executeUpdate();
            return affected > 0;
            
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to save crystal position: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Get all boss positions
     */
    public List<LocationData> getBossPositions() {
        return getPositions("boss_positions");
    }
    
    /**
     * Get all altar positions
     */
    public List<LocationData> getAltarPositions() {
        return getPositions("altar_positions");
    }
    
    /**
     * Get all crystal positions
     */
    public List<LocationData> getCrystalPositions() {
        return getPositions("crystal_positions");
    }
    
    /**
     * Generic method to get positions from a table
     */
    private List<LocationData> getPositions(String tableName) {
        List<LocationData> positions = new ArrayList<>();
        String sql = "SELECT id, world, x, y, z FROM " + tableName;
        
        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                LocationData data = new LocationData(
                    rs.getInt("id"),
                    rs.getString("world"),
                    rs.getDouble("x"),
                    rs.getDouble("y"),
                    rs.getDouble("z")
                );
                positions.add(data);
            }
            
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to get positions from " + tableName + ": " + e.getMessage());
        }
        
        return positions;
    }
    
    /**
     * Remove all setup data
     */
    public boolean removeAllSetupData() {
        try {
            try (Statement stmt = connection.createStatement()) {
                stmt.execute("DELETE FROM boss_positions");
                stmt.execute("DELETE FROM altar_positions");
                stmt.execute("DELETE FROM crystal_positions");
            }
            return true;
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to remove all setup data: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Check if altar exists at location
     */
    public boolean isAltarAt(Location location) {
        String sql = "SELECT COUNT(*) FROM altar_positions WHERE world = ? AND x = ? AND y = ? AND z = ?";
        
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, location.getWorld().getName());
            pstmt.setDouble(2, location.getBlockX());
            pstmt.setDouble(3, location.getBlockY());
            pstmt.setDouble(4, location.getBlockZ());
            
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
            
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to check altar position: " + e.getMessage());
        }
        
        return false;
    }
    
    /**
     * Close database connection
     */
    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                SoulDragonPlugin.info("Database connection closed");
            }
        } catch (SQLException e) {
            SoulDragonPlugin.error("Failed to close database: " + e.getMessage());
        }
    }
    
    /**
     * Data class for location storage
     */
    public static class LocationData {
        private final int id;
        private final String world;
        private final double x, y, z;
        
        public LocationData(int id, String world, double x, double y, double z) {
            this.id = id;
            this.world = world;
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        public int getId() { return id; }
        public String getWorld() { return world; }
        public double getX() { return x; }
        public double getY() { return y; }
        public double getZ() { return z; }
        
        public Location toLocation() {
            return new Location(
                org.bukkit.Bukkit.getWorld(world),
                x, y, z
            );
        }
    }
}
