package shyrcs.dragon.dragon.skills;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

/**
 * Roar skill - Area effect that applies debuffs and knockback
 */
public class RoarSkill extends DragonSkill {
    
    public RoarSkill(SoulDragonPlugin plugin, CustomDragon customDragon) {
        super(plugin, customDragon, "roar");
    }
    
    @Override
    public boolean canUse() {
        // Can use when there are nearby players
        Location dragonLocation = customDragon.getDragon().getLocation();
        double range = getRange();
        
        for (Entity entity : dragonLocation.getWorld().getNearbyEntities(dragonLocation, range, range, range)) {
            if (entity instanceof Player player && 
                player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
                player.getGameMode() != org.bukkit.GameMode.SPECTATOR) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public void use() {
        Location dragonLocation = customDragon.getDragon().getLocation();
        double damage = getDamage();
        double range = getRange();
        double knockback = plugin.getConfig().getDouble("dragon.skills.roar.knockback", 3.0);
        int slownessDuration = plugin.getConfig().getInt("dragon.skills.roar.slowness_duration", 100);
        int weaknessDuration = plugin.getConfig().getInt("dragon.skills.roar.weakness_duration", 100);
        
        // Play roar sound
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_ENDER_DRAGON_GROWL, 3.0f, 0.5f);
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_WITHER_SPAWN, 2.0f, 0.8f);
        
        // Create expanding shockwave effect
        createShockwaveEffect(dragonLocation, range);
        
        // Affect all nearby entities
        for (Entity entity : dragonLocation.getWorld().getNearbyEntities(dragonLocation, range, range, range)) {
            if (entity instanceof LivingEntity livingEntity && 
                !entity.equals(customDragon.getDragon())) {
                
                double distance = entity.getLocation().distance(dragonLocation);
                if (distance <= range) {
                    // Calculate effects based on distance
                    double distanceRatio = distance / range;
                    double actualDamage = damage * (1.0 - distanceRatio);
                    double actualKnockback = knockback * (1.0 - distanceRatio);
                    
                    // Damage
                    livingEntity.damage(actualDamage);
                    
                    // Knockback
                    Vector knockbackVector = entity.getLocation().toVector()
                        .subtract(dragonLocation.toVector())
                        .normalize()
                        .multiply(actualKnockback);
                    
                    entity.setVelocity(knockbackVector);
                    
                    // Apply debuffs to players
                    if (entity instanceof Player player) {
                        // Slowness effect
                        player.addPotionEffect(new PotionEffect(
                            PotionEffectType.SLOWNESS,
                            slownessDuration,
                            2 // Level 3
                        ));
                        
                        // Weakness effect
                        player.addPotionEffect(new PotionEffect(
                            PotionEffectType.WEAKNESS,
                            weaknessDuration,
                            1 // Level 2
                        ));
                        
                        // Blindness for dramatic effect
                        player.addPotionEffect(new PotionEffect(
                            PotionEffectType.BLINDNESS,
                            40, // 2 seconds
                            0   // Level 1
                        ));
                        
                        // Send message
                        player.sendMessage("§4§l🐲 Tiếng gầm của rồng làm bạn sợ hãi!");
                    }
                }
            }
        }
    }
    
    /**
     * Create expanding shockwave particle effect
     */
    private void createShockwaveEffect(Location center, double maxRange) {
        new org.bukkit.scheduler.BukkitRunnable() {
            private double currentRadius = 0;
            private final double step = 1.0;
            
            @Override
            public void run() {
                if (currentRadius > maxRange) {
                    this.cancel();
                    return;
                }
                
                // Create circle of particles
                int particleCount = (int) (currentRadius * 8); // More particles for larger circles
                for (int i = 0; i < particleCount; i++) {
                    double angle = (2 * Math.PI * i) / particleCount;
                    double x = center.getX() + currentRadius * Math.cos(angle);
                    double z = center.getZ() + currentRadius * Math.sin(angle);
                    
                    Location particleLocation = new Location(center.getWorld(), x, center.getY(), z);
                    
                    // Adjust Y to ground level
                    while (particleLocation.getY() > 0 && 
                           !particleLocation.getBlock().getType().isSolid()) {
                        particleLocation.subtract(0, 1, 0);
                    }
                    particleLocation.add(0, 1, 0); // Slightly above ground
                    
                    // Spawn particles
                    center.getWorld().spawnParticle(
                        Particle.EXPLOSION,
                        particleLocation,
                        1,
                        0.1, 0.1, 0.1,
                        0.0
                    );
                    
                    center.getWorld().spawnParticle(
                        Particle.SMOKE,
                        particleLocation,
                        2,
                        0.2, 0.2, 0.2,
                        0.05
                    );
                }
                
                // Create upward particles at center
                center.getWorld().spawnParticle(
                    Particle.CLOUD,
                    center.clone().add(0, 2, 0),
                    10,
                    3.0, 1.0, 3.0,
                    0.1
                );
                
                currentRadius += step;
            }
        }.runTaskTimer(plugin, 0L, 2L); // Update every 2 ticks for smooth expansion
    }
    
    @Override
    public int getCooldownSeconds() {
        return 30; // Moderate cooldown for area effect skill
    }
}
