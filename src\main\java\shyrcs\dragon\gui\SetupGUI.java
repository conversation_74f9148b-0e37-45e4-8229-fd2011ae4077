package shyrcs.dragon.gui;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import shyrcs.dragon.SoulDragonPlugin;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * GUI for quick access to setup items
 */
public class SetupGUI implements Listener {
    
    private final SoulDragonPlugin plugin;
    private final Map<UUID, Inventory> openGUIs;
    
    public SetupGUI(SoulDragonPlugin plugin) {
        this.plugin = plugin;
        this.openGUIs = new HashMap<>();
    }
    
    /**
     * Open setup GUI for player
     */
    public void openSetupGUI(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§6§lDragon Setup - Lấy Items Nhanh");
        
        // Boss Position Item (slot 10)
        ItemStack bossItem = createGUIItem(
            Material.BLAZE_ROD,
            "§c§lQue Quỷ Lửa - Boss Position",
            "§7▶ Click để lấy 1 cái",
            "§7▶ Chuột phải vào block để đánh dấu",
            "§7▶ vị trí triệu hồi boss",
            "§7▶ Boss sẽ spawn 130 blocks phía trên",
            "",
            "§e§lCost: §f1 Fire Rod"
        );
        gui.setItem(10, bossItem);
        
        // Altar Item (slot 12)
        ItemStack altarItem = createGUIItem(
            Material.END_PORTAL_FRAME,
            "§6§lTế Đàn Triệu Hồi",
            "§7▶ Click để lấy 8 Fire Rods",
            "§7▶ Chuột phải vào End Portal Frame",
            "§7▶ để tạo tế đàn triệu hồi",
            "",
            "§e§lCost: §f8 Fire Rods"
        );
        gui.setItem(12, altarItem);
        
        // Crystal Item (slot 14)
        ItemStack crystalItem = createGUIItem(
            Material.END_CRYSTAL,
            "§d§lEnd Crystal Setup",
            "§7▶ Click để lấy 12 Fire Rods",
            "§7▶ Chuột phải vào block để đánh dấu",
            "§7▶ vị trí End Crystal",
            "",
            "§e§lCost: §f12 Fire Rods"
        );
        gui.setItem(14, crystalItem);
        
        // Ender Eye Item (slot 16)
        ItemStack eyeItem = createGUIItem(
            Material.ENDER_EYE,
            "§5§lMắt Ender",
            "§7▶ Click để lấy 8 cái",
            "§7▶ Chuột phải vào tế đàn để đặt",
            "§7▶ Cần 8 mắt để triệu hồi rồng",
            "",
            "§e§lFREE"
        );
        gui.setItem(16, eyeItem);
        
        // Info Item (slot 4)
        ItemStack infoItem = createGUIItem(
            Material.BOOK,
            "§e§lHướng Dẫn Setup",
            "§7§l1. §fĐánh dấu vị trí Boss",
            "§7§l2. §fTạo tế đàn triệu hồi",
            "§7§l3. §fĐánh dấu vị trí End Crystal",
            "§7§l4. §fLấy Mắt Ender để triệu hồi",
            "",
            "§a§lChúc bạn may mắn!"
        );
        gui.setItem(4, infoItem);
        
        // Statistics Item (slot 22)
        int bossCount = plugin.getDatabase().getBossPositions().size();
        int altarCount = plugin.getDatabase().getAltarPositions().size();
        int crystalCount = plugin.getDatabase().getCrystalPositions().size();
        
        ItemStack statsItem = createGUIItem(
            Material.PAPER,
            "§b§lThống Kê Setup",
            "§7Boss Positions: §f" + bossCount,
            "§7Altar Positions: §f" + altarCount,
            "§7Crystal Positions: §f" + crystalCount,
            "",
            "§c/sdc remove all §7- Xóa tất cả"
        );
        gui.setItem(22, statsItem);
        
        // Decorative items
        ItemStack glass = createGUIItem(Material.BLACK_STAINED_GLASS_PANE, " ");
        int[] glassSlots = {0, 1, 2, 3, 5, 6, 7, 8, 9, 11, 13, 15, 17, 18, 19, 20, 21, 23, 24, 25, 26};
        for (int slot : glassSlots) {
            gui.setItem(slot, glass);
        }
        
        openGUIs.put(player.getUniqueId(), gui);
        player.openInventory(gui);
    }
    
    /**
     * Create GUI item with name and lore
     */
    private ItemStack createGUIItem(Material material, String name, String... lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.displayName(net.kyori.adventure.text.Component.text(name));
            if (lore.length > 0) {
                meta.lore(Arrays.stream(lore)
                    .map(line -> net.kyori.adventure.text.Component.text(line))
                    .toList());
            }
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) {
            return;
        }
        
        Inventory clickedInventory = event.getClickedInventory();
        if (clickedInventory == null || !openGUIs.containsValue(clickedInventory)) {
            return;
        }
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            return;
        }
        
        // Check permission
        if (!player.hasPermission("souldragon.setup")) {
            player.sendMessage("§c✗ Bạn không có quyền sử dụng setup items!");
            return;
        }
        
        switch (event.getSlot()) {
            case 10 -> { // Boss Position
                ItemStack bossItem = plugin.getSetupManager().createBossPositionItem();
                player.getInventory().addItem(bossItem);
                player.sendMessage("§a✓ Đã nhận Boss Position Setup Item!");
                player.closeInventory();
            }
            case 12 -> { // Altar
                ItemStack altarItem = plugin.getSetupManager().createAltarItem();
                player.getInventory().addItem(altarItem);
                player.sendMessage("§a✓ Đã nhận Altar Setup Items!");
                player.closeInventory();
            }
            case 14 -> { // Crystal
                ItemStack crystalItem = plugin.getSetupManager().createCrystalItem();
                player.getInventory().addItem(crystalItem);
                player.sendMessage("§a✓ Đã nhận Crystal Setup Items!");
                player.closeInventory();
            }
            case 16 -> { // Ender Eye
                ItemStack eyeItem = plugin.getSetupManager().createEnderEyeItem();
                eyeItem.setAmount(8); // Give 8 eyes
                player.getInventory().addItem(eyeItem);
                player.sendMessage("§a✓ Đã nhận 8 Mắt Ender!");
                player.closeInventory();
            }
            case 4 -> { // Info - do nothing, just display
                player.sendMessage("§e§lHướng dẫn setup đã hiển thị trong GUI!");
            }
            case 22 -> { // Stats - refresh
                openSetupGUI(player); // Refresh GUI with updated stats
            }
        }
    }
    
    /**
     * Remove player from tracking when they close inventory
     */
    public void removePlayer(Player player) {
        openGUIs.remove(player.getUniqueId());
    }
    
    /**
     * Cleanup all open GUIs
     */
    public void cleanup() {
        openGUIs.clear();
    }
}
