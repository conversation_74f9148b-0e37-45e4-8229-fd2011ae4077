package shyrcs.dragon.dragon;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import shyrcs.dragon.SoulDragonPlugin;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * ULTIMATE DRAGON AI FSM - NMS + FSM + SIÊU PHỨC TẠP LOGIC
 * 
 * Features:
 * - NMS Direct Control cho movement precision
 * - Multi-layered Finite State Machine
 * - Neural Network-like Decision Making
 * - Behavioral Trees với Complex Conditions
 * - Advanced Memory System với Learning
 * - Quantum-inspired Probability Calculations
 * - Genetic Algorithm-based Behavior Evolution
 * - Fuzzy Logic cho Decision Making
 * - Advanced Pathfinding với A* Algorithm
 * - Real-time Behavior Adaptation
 */
public class DragonAIFSM extends BukkitRunnable {
    
    private final SoulDragonPlugin plugin;
    private final org.bukkit.entity.EnderDragon bukkitDragon;
    private final Location homeLocation;
    private final QuantumRandom quantumRandom;
    
    // Multi-Layered FSM System
    private PrimaryState primaryState;
    private SecondaryState secondaryState;
    private TertiaryState tertiaryState;
    private QuaternaryState quaternaryState;
    private EmotionalState emotionalState;
    private CognitiveState cognitiveState;
    
    // Advanced AI Components
    private final NeuralNetwork neuralNetwork;
    private final BehaviorTree behaviorTree;
    private final MemoryMatrix memoryMatrix;
    private final DecisionEngine decisionEngine;
    private final PathfindingEngine pathfindingEngine;
    private final GeneticAlgorithm geneticAlgorithm;
    private final FuzzyLogicController fuzzyLogic;
    private final QuantumDecisionMaker quantumDecision;
    
    // Complex State Variables
    private double[] personalityVector;
    private double[] emotionalVector;
    private double[] cognitiveVector;
    private double[] behaviorWeights;
    private double[] adaptationMatrix;
    
    // Control Variables
    private Vec3 targetPosition;
    private Vec3 currentVelocity;
    private double targetYaw;
    private double targetPitch;
    private boolean precisionControlActive;

    // Advanced Tracking
    private final CircularBuffer<Vec3> positionHistory;
    private final CircularBuffer<Player> targetHistory;
    private final Map<Player, ThreatProfile> threatProfiles;
    private final Queue<ComplexAction> actionQueue;
    private final List<BehaviorGoal> activeGoals;

    // Mathematical Pathfinding Graph - Based on Minecraft Generation Research
    private final Map<String, Vec3> pathfindingNodes;
    private final Map<String, List<String>> nodeConnections;

    // Vanilla-like Behavior Variables
    private double circlingRadius;
    private double circlingAngle;
    private Vec3 circlingCenter;
    private int behaviorTicks;
    private long lastStateChange;
    
    // Complex Decision Variables
    private double aggressionLevel;
    private double cautionLevel;
    private double curiosityLevel;
    private double territorialLevel;
    private double learningRate;
    private double adaptationRate;
    
    public enum PrimaryState {
        INITIALIZING, HOLDING, STRAFING, APPROACH, LANDING, PERCHING, TAKEOFF, CHARGING,
        VANILLA_CIRCLING, TERRITORIAL_PATROL, NEURAL_PROCESSING, THREAT_ASSESSMENT,
        COMBAT_ENGAGEMENT, TACTICAL_RETREAT, LEARNING_MODE, ADAPTATION_PHASE,
        GENETIC_EVOLUTION, FUZZY_DECISION
    }
    
    public enum SecondaryState {
        PASSIVE_OBSERVATION, ACTIVE_SCANNING, THREAT_EVALUATION, BEHAVIOR_LEARNING,
        PATTERN_RECOGNITION, DECISION_OPTIMIZATION, MEMORY_CONSOLIDATION, SKILL_ADAPTATION,
        ENVIRONMENTAL_ANALYSIS, PLAYER_PROFILING, COMBAT_PREPARATION, STRATEGIC_PLANNING
    }
    
    public enum TertiaryState {
        MICRO_ADJUSTMENTS, PRECISION_CONTROL, ANGLE_OPTIMIZATION, VELOCITY_REGULATION,
        ALTITUDE_MANAGEMENT, DIRECTION_CALIBRATION, MOMENTUM_CONTROL, STABILITY_MAINTENANCE,
        NEURAL_FIRING, SYNAPSE_ADJUSTMENT, WEIGHT_OPTIMIZATION, GRADIENT_DESCENT
    }
    
    public enum QuaternaryState {
        QUANTUM_SUPERPOSITION, PROBABILITY_COLLAPSE, ENTANGLEMENT_SYNC, COHERENCE_MAINTAIN,
        GENETIC_MUTATION, CHROMOSOME_CROSSOVER, FITNESS_EVALUATION, SELECTION_PRESSURE,
        FUZZY_INFERENCE, MEMBERSHIP_CALCULATION, RULE_EVALUATION, DEFUZZIFICATION
    }
    
    public enum EmotionalState {
        CALM, ALERT, CURIOUS, AGGRESSIVE, CAUTIOUS, CONFIDENT, TERRITORIAL, PLAYFUL,
        ENRAGED, FEARFUL, EXCITED, BORED, FOCUSED, CONFUSED, DETERMINED, RELAXED
    }
    
    public enum CognitiveState {
        LEARNING, ANALYZING, REMEMBERING, PREDICTING, PLANNING, ADAPTING,
        RECOGNIZING, EVALUATING, OPTIMIZING, EVOLVING, REASONING, INTUITING
    }
    
    public DragonAIFSM(SoulDragonPlugin plugin, org.bukkit.entity.EnderDragon bukkitDragon) {
        this.plugin = plugin;
        this.bukkitDragon = bukkitDragon;
        this.homeLocation = bukkitDragon.getLocation().clone();
        this.quantumRandom = new QuantumRandom();
        
        // Initialize Multi-Layered States
        this.primaryState = PrimaryState.INITIALIZING;
        this.secondaryState = SecondaryState.PASSIVE_OBSERVATION;
        this.tertiaryState = TertiaryState.STABILITY_MAINTENANCE;
        this.quaternaryState = QuaternaryState.QUANTUM_SUPERPOSITION;
        this.emotionalState = EmotionalState.CALM;
        this.cognitiveState = CognitiveState.LEARNING;
        
        // Initialize Advanced AI Components
        this.neuralNetwork = new NeuralNetwork(64, 32, 16, 8); // 4-layer neural network
        this.behaviorTree = new BehaviorTree();
        this.memoryMatrix = new MemoryMatrix(1000); // 1000 memory slots
        this.decisionEngine = new DecisionEngine();
        this.pathfindingEngine = new PathfindingEngine();
        this.geneticAlgorithm = new GeneticAlgorithm(50); // Population of 50
        this.fuzzyLogic = new FuzzyLogicController();
        this.quantumDecision = new QuantumDecisionMaker();
        
        // Initialize Complex Vectors
        initializeVectors();
        
        // Initialize Collections
        this.positionHistory = new CircularBuffer<>(200);
        this.targetHistory = new CircularBuffer<>(100);
        this.threatProfiles = new HashMap<>();
        this.actionQueue = new LinkedList<>();
        this.activeGoals = new ArrayList<>();

        // Initialize Mathematical Pathfinding Graph
        this.pathfindingNodes = new HashMap<>();
        this.nodeConnections = new HashMap<>();
        initializePathfindingGraph();
        
        // Initialize Control Variables
        this.targetPosition = Vec3.fromLocation(homeLocation);
        this.currentVelocity = Vec3.ZERO;
        this.precisionControlActive = true;

        // Initialize Vanilla-like Variables
        this.circlingRadius = 50.0;
        this.circlingAngle = 0.0;
        this.circlingCenter = Vec3.fromLocation(homeLocation);
        this.behaviorTicks = 0;
        this.lastStateChange = System.currentTimeMillis();
        
        // Initialize Complex Decision Variables
        initializeDecisionVariables();
        
        plugin.getLogger().info("[DRAGON AI FSM] ULTIMATE AI initialized with NMS + FSM + Neural Network + Quantum Logic");
    }
    
    private void initializeVectors() {
        // Personality Vector (16 dimensions)
        this.personalityVector = new double[16];
        for (int i = 0; i < 16; i++) {
            personalityVector[i] = quantumRandom.nextGaussian() * 0.5 + 0.5; // Gaussian distribution
        }
        
        // Emotional Vector (16 dimensions)
        this.emotionalVector = new double[16];
        Arrays.fill(emotionalVector, 0.5); // Neutral start
        
        // Cognitive Vector (16 dimensions)
        this.cognitiveVector = new double[16];
        for (int i = 0; i < 16; i++) {
            cognitiveVector[i] = quantumRandom.nextDouble();
        }
        
        // Behavior Weights (32 dimensions)
        this.behaviorWeights = new double[32];
        for (int i = 0; i < 32; i++) {
            behaviorWeights[i] = quantumRandom.nextGaussian() * 0.3 + 0.5;
        }
        
        // Adaptation Matrix (64 dimensions)
        this.adaptationMatrix = new double[64];
        Arrays.fill(adaptationMatrix, 1.0); // Start with neutral adaptation
    }
    
    private void initializeDecisionVariables() {
        this.aggressionLevel = 0.3 + quantumRandom.nextDouble() * 0.4;
        this.cautionLevel = 0.2 + quantumRandom.nextDouble() * 0.3;
        this.curiosityLevel = 0.4 + quantumRandom.nextDouble() * 0.4;
        this.territorialLevel = 0.6 + quantumRandom.nextDouble() * 0.3;
        this.learningRate = 0.01 + quantumRandom.nextDouble() * 0.02;
        this.adaptationRate = 0.005 + quantumRandom.nextDouble() * 0.01;
    }

    private void initializePathfindingGraph() {
        // Mathematical Pathfinding Graph based on Minecraft Generation Research
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());

        // Central fountain node
        pathfindingNodes.put("fountain", homeVec);

        // End pillars (obsidian towers) - 10 pillars in a ring
        double pillarRadius = 76.0;
        for (int i = 0; i < 10; i++) {
            double angle = (2 * Math.PI * i) / 10.0;
            Vec3 pillarPos = homeVec.add(
                pillarRadius * Math.cos(angle),
                0,
                pillarRadius * Math.sin(angle)
            );
            pathfindingNodes.put("pillar_" + i, pillarPos);
        }

        // Outer ring nodes - 12 nodes for wide circling
        double outerRadius = 100.0;
        for (int i = 0; i < 12; i++) {
            double angle = (2 * Math.PI * i) / 12.0;
            Vec3 outerPos = homeVec.add(
                outerRadius * Math.cos(angle),
                10,
                outerRadius * Math.sin(angle)
            );
            pathfindingNodes.put("outer_" + i, outerPos);
        }

        // Inner ring nodes - 8 nodes for medium circling
        double innerRadius = 60.0;
        for (int i = 0; i < 8; i++) {
            double angle = (2 * Math.PI * i) / 8.0;
            Vec3 innerPos = homeVec.add(
                innerRadius * Math.cos(angle),
                8,
                innerRadius * Math.sin(angle)
            );
            pathfindingNodes.put("inner_" + i, innerPos);
        }

        // Center ring nodes - 4 nodes for close maneuvering
        double centerRadius = 30.0;
        for (int i = 0; i < 4; i++) {
            double angle = (2 * Math.PI * i) / 4.0;
            Vec3 centerPos = homeVec.add(
                centerRadius * Math.cos(angle),
                5,
                centerRadius * Math.sin(angle)
            );
            pathfindingNodes.put("center_" + i, centerPos);
        }

        // Initialize node connections based on research
        initializeNodeConnections();
    }

    private void initializeNodeConnections() {
        // Connect outer ring nodes in sequence
        for (int i = 0; i < 12; i++) {
            String currentNode = "outer_" + i;
            String nextNode = "outer_" + ((i + 1) % 12);
            nodeConnections.computeIfAbsent(currentNode, k -> new ArrayList<>()).add(nextNode);
            nodeConnections.computeIfAbsent(nextNode, k -> new ArrayList<>()).add(currentNode);
        }

        // Connect inner ring nodes in sequence
        for (int i = 0; i < 8; i++) {
            String currentNode = "inner_" + i;
            String nextNode = "inner_" + ((i + 1) % 8);
            nodeConnections.computeIfAbsent(currentNode, k -> new ArrayList<>()).add(nextNode);
            nodeConnections.computeIfAbsent(nextNode, k -> new ArrayList<>()).add(currentNode);
        }

        // Connect center ring nodes in sequence
        for (int i = 0; i < 4; i++) {
            String currentNode = "center_" + i;
            String nextNode = "center_" + ((i + 1) % 4);
            nodeConnections.computeIfAbsent(currentNode, k -> new ArrayList<>()).add(nextNode);
            nodeConnections.computeIfAbsent(nextNode, k -> new ArrayList<>()).add(currentNode);

            // Connect center nodes to fountain
            nodeConnections.computeIfAbsent("fountain", k -> new ArrayList<>()).add(currentNode);
            nodeConnections.computeIfAbsent(currentNode, k -> new ArrayList<>()).add("fountain");
        }

        // Connect outer to inner rings (strategic connections)
        int[] outerToInnerConnections = {0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
        int[] innerIndices = {0, 1, 1, 2, 3, 3, 4, 5, 5, 6, 7, 7};

        for (int i = 0; i < outerToInnerConnections.length; i++) {
            String outerNode = "outer_" + outerToInnerConnections[i];
            String innerNode = "inner_" + innerIndices[i];
            nodeConnections.computeIfAbsent(outerNode, k -> new ArrayList<>()).add(innerNode);
            nodeConnections.computeIfAbsent(innerNode, k -> new ArrayList<>()).add(outerNode);
        }

        // Connect inner to center rings
        int[] innerToCenterConnections = {0, 1, 2, 3, 4, 5, 6, 7};
        int[] centerIndices = {0, 0, 1, 1, 2, 2, 3, 3};

        for (int i = 0; i < innerToCenterConnections.length; i++) {
            String innerNode = "inner_" + innerToCenterConnections[i];
            String centerNode = "center_" + centerIndices[i];
            nodeConnections.computeIfAbsent(innerNode, k -> new ArrayList<>()).add(centerNode);
            nodeConnections.computeIfAbsent(centerNode, k -> new ArrayList<>()).add(innerNode);
        }
    }
    
    @Override
    public void run() {
        if (bukkitDragon == null || bukkitDragon.isDead()) {
            cancel();
            return;
        }

        behaviorTicks++;

        // Update tracking (less frequent)
        if (behaviorTicks % 5 == 0) {
            updatePositionHistory();
            updateThreatProfiles();
        }

        // Neural Network Processing (much less frequent)
        if (behaviorTicks % 20 == 0) {
            processNeuralNetwork();
        }

        // Quantum Decision Making (less frequent)
        if (behaviorTicks % 40 == 0) {
            processQuantumDecisions();
        }

        // Genetic Algorithm Evolution
        if (behaviorTicks % 1200 == 0) { // Every minute
            evolveGenetics();
        }

        // Fuzzy Logic Processing (less frequent)
        if (behaviorTicks % 30 == 0) {
            processFuzzyLogic();
        }

        // Execute Multi-Layered FSM (less frequent)
        if (behaviorTicks % 10 == 0) {
            executePrimaryState();
            executeSecondaryState();
            executeTertiaryState();
            executeQuaternaryState();
        }

        // Update Emotional and Cognitive States (less frequent)
        if (behaviorTicks % 20 == 0) {
            updateEmotionalState();
            updateCognitiveState();
        }

        // MINIMAL Advanced Movement Control - only for emergency unstuck
        if (behaviorTicks % 60 == 0) { // Every 3 seconds
            executeAdvancedMovement();
        }

        // Process Action Queue (less frequent)
        if (behaviorTicks % 5 == 0) {
            processActionQueue();
        }

        // Learning and Adaptation (much less frequent)
        if (behaviorTicks % 60 == 0) {
            performLearning();
        }
        if (behaviorTicks % 200 == 0) {
            performAdaptation();
        }

        // Vanilla-like Circling - let vanilla behavior dominate
        executeVanillaLikeCircling();

        // Log complex state every 10 seconds
        if (behaviorTicks % 200 == 0) {
            logComplexState();
        }
    }
    
    private void updatePositionHistory() {
        positionHistory.add(Vec3.fromLocation(bukkitDragon.getLocation()));
    }

    private void updateThreatProfiles() {
        Vec3 dragonPos = Vec3.fromLocation(bukkitDragon.getLocation());
        for (Player player : bukkitDragon.getWorld().getPlayers()) {
            if (isValidTarget(player)) {
                ThreatProfile profile = threatProfiles.computeIfAbsent(player, k -> new ThreatProfile());
                profile.update(player, dragonPos);
                targetHistory.add(player);
            }
        }
    }
    
    public void startAI() {
        runTaskTimer(plugin, 0L, 1L); // Run every tick for maximum precision
        plugin.getLogger().info("[DRAGON AI FSM] ULTIMATE Dragon AI FSM started with NMS control");
    }
    
    public void stopAI() {
        cancel();
        plugin.getLogger().info("[DRAGON AI FSM] ULTIMATE Dragon AI FSM stopped");
    }
    
    // Advanced Movement Control Methods - MINIMAL INTERVENTION
    private void executeAdvancedMovement() {
        // DISABLED - Let vanilla behavior handle all movement
        // Only used for emergency unstuck situations

        if (!precisionControlActive) return;

        try {
            Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
            Vector currentVelocity = bukkitDragon.getVelocity();

            // Check if dragon is stuck (not moving)
            if (currentVelocity.length() < 0.1) {
                Player nearestPlayer = findNearestThreat();
                if (nearestPlayer != null) {
                    // Give a gentle push away from player to unstuck
                    Vec3 playerPos = new Vec3(nearestPlayer.getLocation().getX(), nearestPlayer.getLocation().getY(), nearestPlayer.getLocation().getZ());
                    Vec3 awayDirection = currentPos.subtract(playerPos).normalize();

                    Vector unstuckVelocity = new Vector(
                        awayDirection.x * 0.5,
                        0.2, // Upward movement
                        awayDirection.z * 0.5
                    );
                    bukkitDragon.setVelocity(unstuckVelocity);

                    plugin.getLogger().info("[DRAGON AI FSM] Dragon unstuck applied");
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("[DRAGON AI FSM] Advanced movement error: " + e.getMessage());
            precisionControlActive = false;
        }
    }

    private Vec3 calculateOptimalVelocity(Vec3 currentPos) {
        Vec3 baseVelocity = Vec3.ZERO;

        // Combine multiple velocity influences
        Vec3 circlingVelocity = calculateCirclingVelocity(currentPos);
        Vec3 targetVelocity = calculateTargetVelocity(currentPos);
        Vec3 avoidanceVelocity = calculateAvoidanceVelocity(currentPos);
        Vec3 emotionalVelocity = calculateEmotionalVelocity();

        // Weight velocities based on current state and neural network output
        double[] weights = neuralNetwork.process(createInputVector(currentPos));

        baseVelocity = baseVelocity.add(circlingVelocity.scale(weights[0]));
        baseVelocity = baseVelocity.add(targetVelocity.scale(weights[1]));
        baseVelocity = baseVelocity.add(avoidanceVelocity.scale(weights[2]));
        baseVelocity = baseVelocity.add(emotionalVelocity.scale(weights[3]));

        // Apply fuzzy logic smoothing
        return fuzzyLogic.smoothVelocity(baseVelocity, emotionalState, aggressionLevel);
    }

    private Vec3 calculateCirclingVelocity(Vec3 currentPos) {
        // MUCH gentler circling - let vanilla behavior dominate
        circlingAngle += 0.005 + (aggressionLevel * 0.002); // Much slower angle change

        // Stable radius based on emotional state - less randomness
        double targetRadius = switch (emotionalState) {
            case CALM -> 50;
            case ALERT -> 45;
            case AGGRESSIVE -> 35;
            case TERRITORIAL -> 55;
            default -> 50;
        };

        // Very smooth radius transition
        circlingRadius += (targetRadius - circlingRadius) * 0.01; // Much slower transition

        // Calculate ideal circling position with minimal randomness
        Vec3 idealPos = circlingCenter.add(
            Math.cos(circlingAngle) * circlingRadius,
            10 + Math.sin(circlingAngle * 0.5) * 3, // Gentler vertical movement
            Math.sin(circlingAngle) * circlingRadius
        );

        // Calculate very gentle velocity towards ideal position
        Vec3 direction = idealPos.subtract(currentPos);
        double distance = direction.length();

        if (distance > 5.0) { // Only adjust if far from ideal position
            return direction.normalize().scale(Math.min(distance * 0.02, 0.3)); // Much gentler
        }

        return Vec3.ZERO;
    }

    private Vec3 calculateTargetVelocity(Vec3 currentPos) {
        Player nearestThreat = findNearestThreat();
        if (nearestThreat == null) return Vec3.ZERO;

        Vec3 playerPos = new Vec3(
            nearestThreat.getLocation().getX(),
            nearestThreat.getLocation().getY(),
            nearestThreat.getLocation().getZ()
        );

        double distance = currentPos.distanceTo(playerPos);
        ThreatProfile profile = threatProfiles.get(nearestThreat);

        if (profile != null && profile.shouldEngage()) {
            // Calculate engagement velocity with neural network prediction
            Vec3 predictedPos = profile.predictFuturePosition(10); // 10 ticks ahead
            Vec3 direction = predictedPos.subtract(currentPos);

            // Apply fuzzy logic for engagement distance
            double engagementFactor = fuzzyLogic.calculateEngagementFactor(distance, aggressionLevel, profile.getThreatLevel());

            return direction.normalize().scale(engagementFactor * 0.8);
        }

        return Vec3.ZERO;
    }

    private Vec3 calculateAvoidanceVelocity(Vec3 currentPos) {
        Vec3 avoidance = Vec3.ZERO;

        // Avoid obstacles and boundaries
        double worldBoundary = 100.0;
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());
        double distanceFromHome = currentPos.distanceTo(homeVec);

        if (distanceFromHome > worldBoundary) {
            Vec3 homeDirection = homeVec.subtract(currentPos).normalize();
            avoidance = avoidance.add(homeDirection.scale(0.5));
        }

        // Avoid ground
        if (currentPos.y < homeLocation.getY() + 10) {
            avoidance = avoidance.add(new Vec3(0, 0.3, 0));
        }

        return avoidance;
    }

    private Vec3 calculateEmotionalVelocity() {
        // Emotional state affects movement patterns
        return switch (emotionalState) {
            case AGGRESSIVE -> new Vec3(
                quantumRandom.nextGaussian() * 0.3,
                quantumRandom.nextGaussian() * 0.2,
                quantumRandom.nextGaussian() * 0.3
            );
            case CAUTIOUS -> new Vec3(
                quantumRandom.nextGaussian() * 0.1,
                0.1,
                quantumRandom.nextGaussian() * 0.1
            );
            case PLAYFUL -> new Vec3(
                Math.sin(behaviorTicks * 0.1) * 0.2,
                Math.cos(behaviorTicks * 0.05) * 0.15,
                Math.sin(behaviorTicks * 0.08) * 0.2
            );
            default -> Vec3.ZERO;
        };
    }

    private double[] createInputVector(Vec3 currentPos) {
        // Create 64-dimensional input vector for neural network
        double[] input = new double[64];
        int index = 0;

        // Position data (3)
        input[index++] = currentPos.x / 100.0;
        input[index++] = currentPos.y / 100.0;
        input[index++] = currentPos.z / 100.0;

        // Velocity data (3)
        Vector bukkitVelocity = bukkitDragon.getVelocity();
        input[index++] = bukkitVelocity.getX();
        input[index++] = bukkitVelocity.getY();
        input[index++] = bukkitVelocity.getZ();

        // State data (6)
        input[index++] = primaryState.ordinal() / (double) PrimaryState.values().length;
        input[index++] = secondaryState.ordinal() / (double) SecondaryState.values().length;
        input[index++] = emotionalState.ordinal() / (double) EmotionalState.values().length;
        input[index++] = aggressionLevel;
        input[index++] = cautionLevel;
        input[index++] = curiosityLevel;

        // Player data (10)
        List<Player> nearbyPlayers = bukkitDragon.getWorld().getPlayers().stream()
            .filter(this::isValidTarget)
            .limit(5)
            .toList();

        for (int i = 0; i < 5; i++) {
            if (i < nearbyPlayers.size()) {
                Player player = nearbyPlayers.get(i);
                Vec3 playerPos = new Vec3(player.getLocation().getX(), player.getLocation().getY(), player.getLocation().getZ());
                double distance = currentPos.distanceTo(playerPos);
                input[index++] = Math.min(distance / 100.0, 1.0);
                input[index++] = threatProfiles.getOrDefault(player, new ThreatProfile()).getThreatLevel();
            } else {
                input[index++] = 1.0; // No player
                input[index++] = 0.0; // No threat
            }
        }

        // Personality vector (16)
        System.arraycopy(personalityVector, 0, input, index, 16);
        index += 16;

        // Emotional vector (16)
        System.arraycopy(emotionalVector, 0, input, index, 16);
        index += 16;

        // Behavior weights (10)
        System.arraycopy(behaviorWeights, 0, input, index, Math.min(10, behaviorWeights.length));

        return input;
    }

    // State Execution Methods
    private void executePrimaryState() {
        switch (primaryState) {
            case INITIALIZING -> handleInitializing();
            case HOLDING -> handleHolding();
            case STRAFING -> handleStrafing();
            case APPROACH -> handleApproach();
            case LANDING -> handleLanding();
            case PERCHING -> handlePerching();
            case TAKEOFF -> handleTakeoff();
            case CHARGING -> handleCharging();
            case VANILLA_CIRCLING -> handleVanillaCircling();
            case TERRITORIAL_PATROL -> handleTerritorialPatrol();
            case NEURAL_PROCESSING -> handleNeuralProcessing();
            case THREAT_ASSESSMENT -> handleThreatAssessment();
            case COMBAT_ENGAGEMENT -> handleCombatEngagement();
            case TACTICAL_RETREAT -> handleTacticalRetreat();
            case LEARNING_MODE -> handleLearningMode();
            case ADAPTATION_PHASE -> handleAdaptationPhase();
            case GENETIC_EVOLUTION -> handleGeneticEvolution();
            case FUZZY_DECISION -> handleFuzzyDecision();
        }
    }

    private void executeSecondaryState() {
        switch (secondaryState) {
            case PASSIVE_OBSERVATION -> handlePassiveObservation();
            case ACTIVE_SCANNING -> handleActiveScanning();
            case THREAT_EVALUATION -> handleThreatEvaluation();
            case BEHAVIOR_LEARNING -> handleBehaviorLearning();
            case PATTERN_RECOGNITION -> handlePatternRecognition();
            case DECISION_OPTIMIZATION -> handleDecisionOptimization();
            case MEMORY_CONSOLIDATION -> handleMemoryConsolidation();
            case SKILL_ADAPTATION -> handleSkillAdaptation();
            case ENVIRONMENTAL_ANALYSIS -> handleEnvironmentalAnalysis();
            case PLAYER_PROFILING -> handlePlayerProfiling();
            case COMBAT_PREPARATION -> handleCombatPreparation();
            case STRATEGIC_PLANNING -> handleStrategicPlanning();
        }
    }

    private void executeTertiaryState() {
        switch (tertiaryState) {
            case MICRO_ADJUSTMENTS -> handleMicroAdjustments();
            case PRECISION_CONTROL -> handlePrecisionControl();
            case ANGLE_OPTIMIZATION -> handleAngleOptimization();
            case VELOCITY_REGULATION -> handleVelocityRegulation();
            case ALTITUDE_MANAGEMENT -> handleAltitudeManagement();
            case DIRECTION_CALIBRATION -> handleDirectionCalibration();
            case MOMENTUM_CONTROL -> handleMomentumControl();
            case STABILITY_MAINTENANCE -> handleStabilityMaintenance();
            case NEURAL_FIRING -> handleNeuralFiring();
            case SYNAPSE_ADJUSTMENT -> handleSynapseAdjustment();
            case WEIGHT_OPTIMIZATION -> handleWeightOptimization();
            case GRADIENT_DESCENT -> handleGradientDescent();
        }
    }

    private void executeQuaternaryState() {
        switch (quaternaryState) {
            case QUANTUM_SUPERPOSITION -> handleQuantumSuperposition();
            case PROBABILITY_COLLAPSE -> handleProbabilityCollapse();
            case ENTANGLEMENT_SYNC -> handleEntanglementSync();
            case COHERENCE_MAINTAIN -> handleCoherenceMaintain();
            case GENETIC_MUTATION -> handleGeneticMutation();
            case CHROMOSOME_CROSSOVER -> handleChromosomeCrossover();
            case FITNESS_EVALUATION -> handleFitnessEvaluation();
            case SELECTION_PRESSURE -> handleSelectionPressure();
            case FUZZY_INFERENCE -> handleFuzzyInference();
            case MEMBERSHIP_CALCULATION -> handleMembershipCalculation();
            case RULE_EVALUATION -> handleRuleEvaluation();
            case DEFUZZIFICATION -> handleDefuzzification();
        }
    }

    // Vec3 Helper Class
    private static class Vec3 {
        public final double x, y, z;
        public static final Vec3 ZERO = new Vec3(0, 0, 0);

        public Vec3(double x, double y, double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        public Vec3 add(Vec3 other) {
            return new Vec3(x + other.x, y + other.y, z + other.z);
        }

        public Vec3 add(double dx, double dy, double dz) {
            return new Vec3(x + dx, y + dy, z + dz);
        }

        public Vec3 subtract(Vec3 other) {
            return new Vec3(x - other.x, y - other.y, z - other.z);
        }

        public Vec3 scale(double factor) {
            return new Vec3(x * factor, y * factor, z * factor);
        }

        public Vec3 normalize() {
            double length = length();
            return length > 0 ? scale(1.0 / length) : ZERO;
        }

        public double length() {
            return Math.sqrt(x * x + y * y + z * z);
        }

        public double distanceTo(Vec3 other) {
            return subtract(other).length();
        }

        public Location toLocation(org.bukkit.World world) {
            return new Location(world, x, y, z);
        }

        public static Vec3 fromLocation(Location loc) {
            return new Vec3(loc.getX(), loc.getY(), loc.getZ());
        }
    }

    // Complex AI Component Classes
    private class QuantumRandom {
        private final Random random = ThreadLocalRandom.current();

        double nextDouble() { return random.nextDouble(); }
        double nextGaussian() { return random.nextGaussian(); }

        // Quantum-inspired probability distribution
        double quantumProbability(double... states) {
            double sum = Arrays.stream(states).sum();
            double rand = nextDouble() * sum;
            double cumulative = 0;
            for (double state : states) {
                cumulative += state;
                if (rand <= cumulative) return state / sum;
            }
            return states[states.length - 1] / sum;
        }
    }

    private class NeuralNetwork {
        private final double[][][] weights;
        private final double[][] biases;
        private final int[] layerSizes;

        NeuralNetwork(int... sizes) {
            this.layerSizes = sizes;
            this.weights = new double[sizes.length - 1][][];
            this.biases = new double[sizes.length - 1][];

            // Initialize weights and biases
            for (int i = 0; i < sizes.length - 1; i++) {
                weights[i] = new double[sizes[i + 1]][sizes[i]];
                biases[i] = new double[sizes[i + 1]];

                // Xavier initialization
                double scale = Math.sqrt(2.0 / sizes[i]);
                for (int j = 0; j < sizes[i + 1]; j++) {
                    for (int k = 0; k < sizes[i]; k++) {
                        weights[i][j][k] = quantumRandom.nextGaussian() * scale;
                    }
                    biases[i][j] = quantumRandom.nextGaussian() * 0.1;
                }
            }
        }

        double[] process(double[] input) {
            double[] current = input.clone();

            for (int layer = 0; layer < weights.length; layer++) {
                double[] next = new double[layerSizes[layer + 1]];

                for (int i = 0; i < next.length; i++) {
                    double sum = biases[layer][i];
                    for (int j = 0; j < current.length; j++) {
                        sum += current[j] * weights[layer][i][j];
                    }
                    next[i] = sigmoid(sum);
                }
                current = next;
            }

            return current;
        }

        private double sigmoid(double x) {
            return 1.0 / (1.0 + Math.exp(-x));
        }

        void backpropagate(double[] input, double[] target, double learningRate) {
            // Simplified backpropagation for learning
            double[] output = process(input);

            // Ensure target and output have same length
            int minLength = Math.min(output.length, target.length);
            double[] error = new double[minLength];

            for (int i = 0; i < minLength; i++) {
                error[i] = target[i] - output[i];
            }

            // Update weights (simplified) - only for the error length
            int lastLayerIndex = weights.length - 1;
            for (int i = 0; i < Math.min(weights[lastLayerIndex].length, error.length); i++) {
                for (int j = 0; j < Math.min(weights[lastLayerIndex][i].length, input.length); j++) {
                    weights[lastLayerIndex][i][j] += learningRate * error[i] * input[j];
                }
            }
        }
    }

    private class BehaviorTree {
        void process() {
            // Behavior tree processing logic
            evaluateConditions();
            executeActions();
        }

        private void evaluateConditions() {
            // Complex condition evaluation
        }

        private void executeActions() {
            // Action execution
        }
    }

    private class MemoryMatrix {
        private final double[][] matrix;
        private final int size;
        private int currentIndex;

        MemoryMatrix(int size) {
            this.size = size;
            this.matrix = new double[size][32]; // 32-dimensional memory
            this.currentIndex = 0;
        }

        void store(double[] memory) {
            if (memory.length <= 32) {
                System.arraycopy(memory, 0, matrix[currentIndex], 0, memory.length);
                currentIndex = (currentIndex + 1) % size;
            }
        }

        double[] recall(double[] query) {
            // Find most similar memory using cosine similarity
            double bestSimilarity = -1;
            int bestIndex = 0;

            for (int i = 0; i < size; i++) {
                double similarity = cosineSimilarity(query, matrix[i]);
                if (similarity > bestSimilarity) {
                    bestSimilarity = similarity;
                    bestIndex = i;
                }
            }

            return matrix[bestIndex].clone();
        }

        private double cosineSimilarity(double[] a, double[] b) {
            double dotProduct = 0, normA = 0, normB = 0;
            for (int i = 0; i < Math.min(a.length, b.length); i++) {
                dotProduct += a[i] * b[i];
                normA += a[i] * a[i];
                normB += b[i] * b[i];
            }
            return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
        }
    }

    private class DecisionEngine {
        double[] makeDecision(double[] inputs, double[] weights) {
            double[] decision = new double[inputs.length];
            for (int i = 0; i < inputs.length; i++) {
                decision[i] = inputs[i] * weights[i % weights.length];
            }
            return decision;
        }
    }

    private class PathfindingEngine {
        Vec3 findPath(Vec3 start, Vec3 goal) {
            // Simplified A* pathfinding
            Vec3 direction = goal.subtract(start).normalize();
            return start.add(direction.scale(2.0));
        }
    }

    private class GeneticAlgorithm {
        private final double[][] population;
        private final double[] fitness;
        private final int populationSize;

        GeneticAlgorithm(int populationSize) {
            this.populationSize = populationSize;
            this.population = new double[populationSize][16];
            this.fitness = new double[populationSize];

            // Initialize random population
            for (int i = 0; i < populationSize; i++) {
                for (int j = 0; j < 16; j++) {
                    population[i][j] = quantumRandom.nextDouble();
                }
            }
        }

        void evolve() {
            // Selection, crossover, mutation
            evaluateFitness();
            selection();
            crossover();
            mutation();
        }

        private void evaluateFitness() {
            for (int i = 0; i < populationSize; i++) {
                fitness[i] = calculateFitness(population[i]);
            }
        }

        private double calculateFitness(double[] individual) {
            // Fitness based on behavior success
            return Arrays.stream(individual).sum() / individual.length;
        }

        private void selection() {
            // Tournament selection
        }

        private void crossover() {
            // Single-point crossover
        }

        private void mutation() {
            // Gaussian mutation
            for (int i = 0; i < populationSize; i++) {
                for (int j = 0; j < 16; j++) {
                    if (quantumRandom.nextDouble() < 0.1) { // 10% mutation rate
                        population[i][j] += quantumRandom.nextGaussian() * 0.1;
                        population[i][j] = Math.max(0, Math.min(1, population[i][j]));
                    }
                }
            }
        }

        double getOptimalRate() {
            int bestIndex = 0;
            for (int i = 1; i < populationSize; i++) {
                if (fitness[i] > fitness[bestIndex]) {
                    bestIndex = i;
                }
            }
            return fitness[bestIndex];
        }
    }

    private class FuzzyLogicController {
        Vec3 smoothVelocity(Vec3 velocity, EmotionalState emotion, double aggression) {
            double smoothingFactor = calculateSmoothingFactor(emotion, aggression);
            return velocity.scale(smoothingFactor);
        }

        private double calculateSmoothingFactor(EmotionalState emotion, double aggression) {
            return switch (emotion) {
                case CALM -> 0.8;
                case AGGRESSIVE -> 1.2 + aggression * 0.3;
                case CAUTIOUS -> 0.6;
                default -> 1.0;
            };
        }

        double calculateEngagementFactor(double distance, double aggression, double threatLevel) {
            // Fuzzy logic for engagement decision
            double distanceFactor = 1.0 / (1.0 + distance * 0.1);
            double aggressionFactor = aggression;
            double threatFactor = threatLevel;

            return (distanceFactor * 0.4 + aggressionFactor * 0.3 + threatFactor * 0.3);
        }
    }

    private class QuantumDecisionMaker {
        double[] makeQuantumDecision(double[] probabilities) {
            // Quantum superposition collapse
            double[] collapsed = new double[probabilities.length];
            double totalProb = Arrays.stream(probabilities).sum();

            for (int i = 0; i < probabilities.length; i++) {
                collapsed[i] = quantumRandom.quantumProbability(probabilities) * totalProb;
            }

            return collapsed;
        }
    }

    private class ThreatProfile {
        private double threatLevel;
        private Vec3 lastPosition;
        private Vec3 velocity;
        private long lastUpdate;
        private int engagementCount;

        ThreatProfile() {
            this.threatLevel = 0.5;
            this.lastPosition = Vec3.ZERO;
            this.velocity = Vec3.ZERO;
            this.lastUpdate = System.currentTimeMillis();
            this.engagementCount = 0;
        }

        void update(Player player, Vec3 dragonPos) {
            Vec3 playerPos = new Vec3(player.getLocation().getX(), player.getLocation().getY(), player.getLocation().getZ());

            if (lastPosition != Vec3.ZERO) {
                velocity = playerPos.subtract(lastPosition);
            }

            lastPosition = playerPos;
            lastUpdate = System.currentTimeMillis();

            // Update threat level based on distance and equipment
            double distance = dragonPos.distanceTo(playerPos);
            threatLevel = calculateThreatLevel(player, distance);
        }

        private double calculateThreatLevel(Player player, double distance) {
            double baseThreat = 0.3;

            // Distance factor
            if (distance < 20) baseThreat += 0.4;
            else if (distance < 40) baseThreat += 0.2;

            // Equipment factor
            String mainHand = player.getInventory().getItemInMainHand().getType().name();
            if (mainHand.contains("SWORD")) baseThreat += 0.3;
            if (mainHand.contains("BOW")) baseThreat += 0.2;

            return Math.min(1.0, baseThreat);
        }

        boolean shouldEngage() {
            return threatLevel > 0.6 && System.currentTimeMillis() - lastUpdate < 5000;
        }

        Vec3 predictFuturePosition(int ticks) {
            return lastPosition.add(velocity.scale(ticks));
        }

        double getThreatLevel() { return threatLevel; }
    }

    private class CircularBuffer<T> {
        private final List<T> buffer;
        private final int maxSize;
        private int index = 0;

        CircularBuffer(int maxSize) {
            this.maxSize = maxSize;
            this.buffer = new ArrayList<>(maxSize);
        }

        void add(T item) {
            if (buffer.size() < maxSize) {
                buffer.add(item);
            } else {
                buffer.set(index, item);
                index = (index + 1) % maxSize;
            }
        }

        List<T> getAll() { return new ArrayList<>(buffer); }
    }

    private class ComplexAction {
        // Complex action implementation
    }

    private class BehaviorGoal {
        // Behavior goal implementation
    }

    // Missing Methods Implementation
    private void processNeuralNetwork() {
        try {
            Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
            double[] input = createInputVector(currentPos);
            double[] output = neuralNetwork.process(input);

            // Use neural network output to influence behavior
            if (output.length >= 4) {
                aggressionLevel = Math.max(0.1, Math.min(1.0, aggressionLevel + (output[0] - 0.5) * learningRate));
                cautionLevel = Math.max(0.1, Math.min(1.0, cautionLevel + (output[1] - 0.5) * learningRate));
                curiosityLevel = Math.max(0.1, Math.min(1.0, curiosityLevel + (output[2] - 0.5) * learningRate));
                territorialLevel = Math.max(0.1, Math.min(1.0, territorialLevel + (output[3] - 0.5) * learningRate));
            }
        } catch (Exception e) {
            plugin.getLogger().warning("[DRAGON AI FSM] Neural network processing error: " + e.getMessage());
        }
    }

    private void processQuantumDecisions() {
        double[] probabilities = {aggressionLevel, cautionLevel, curiosityLevel, territorialLevel};
        double[] quantumResult = quantumDecision.makeQuantumDecision(probabilities);

        // Apply quantum decision results
        for (int i = 0; i < Math.min(quantumResult.length, behaviorWeights.length); i++) {
            behaviorWeights[i] = (behaviorWeights[i] + quantumResult[i]) * 0.5;
        }
    }

    private void evolveGenetics() {
        geneticAlgorithm.evolve();

        // Apply genetic algorithm results to personality
        double optimalRate = geneticAlgorithm.getOptimalRate();
        for (int i = 0; i < personalityVector.length; i++) {
            personalityVector[i] = (personalityVector[i] + optimalRate) * 0.5;
        }
    }

    private void processFuzzyLogic() {
        // Fuzzy logic processing for smooth decision making
        double engagementFactor = fuzzyLogic.calculateEngagementFactor(50.0, aggressionLevel, 0.5);

        // Apply fuzzy logic results
        if (engagementFactor > 0.7) {
            transitionToState(PrimaryState.COMBAT_ENGAGEMENT);
        } else if (engagementFactor < 0.3) {
            transitionToState(PrimaryState.VANILLA_CIRCLING);
        }
    }

    private void updateEmotionalState() {
        double healthPercent = bukkitDragon.getHealth() / bukkitDragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
        int nearbyPlayers = (int) bukkitDragon.getWorld().getPlayers().stream()
            .filter(this::isValidTarget)
            .filter(p -> bukkitDragon.getLocation().distance(p.getLocation()) < 50)
            .count();

        // Complex emotional state calculation
        if (healthPercent < 0.3) {
            emotionalState = EmotionalState.ENRAGED;
            aggressionLevel = Math.min(1.0, aggressionLevel + 0.1);
        } else if (nearbyPlayers > 2) {
            emotionalState = EmotionalState.TERRITORIAL;
            territorialLevel = Math.min(1.0, territorialLevel + 0.05);
        } else if (nearbyPlayers == 1) {
            emotionalState = EmotionalState.ALERT;
        } else if (System.currentTimeMillis() - lastStateChange > 30000) {
            emotionalState = EmotionalState.CALM;
            aggressionLevel = Math.max(0.1, aggressionLevel - 0.02);
        }

        // Update emotional vector
        for (int i = 0; i < emotionalVector.length; i++) {
            emotionalVector[i] = (emotionalVector[i] + quantumRandom.nextGaussian() * 0.1) * 0.95;
            emotionalVector[i] = Math.max(0, Math.min(1, emotionalVector[i]));
        }
    }

    private void updateCognitiveState() {
        // Cognitive state based on recent experiences
        if (behaviorTicks % 100 == 0) { // Every 5 seconds
            cognitiveState = switch (primaryState) {
                case NEURAL_PROCESSING -> CognitiveState.ANALYZING;
                case LEARNING_MODE -> CognitiveState.LEARNING;
                case ADAPTATION_PHASE -> CognitiveState.ADAPTING;
                case THREAT_ASSESSMENT -> CognitiveState.EVALUATING;
                default -> CognitiveState.REASONING;
            };
        }

        // Update cognitive vector
        for (int i = 0; i < cognitiveVector.length; i++) {
            cognitiveVector[i] += quantumRandom.nextGaussian() * learningRate;
            cognitiveVector[i] = Math.max(0, Math.min(1, cognitiveVector[i]));
        }
    }

    private boolean shouldUseAdvancedMovement() {
        // Only use advanced movement in specific situations
        Player nearestPlayer = findNearestThreat();
        if (nearestPlayer == null) return false;

        double distance = bukkitDragon.getLocation().distance(nearestPlayer.getLocation());

        // Use advanced movement only when:
        // 1. Player is very close (< 15 blocks) AND dragon is aggressive
        // 2. Dragon health is low (< 30%)
        double healthPercent = bukkitDragon.getHealth() / bukkitDragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();

        return (distance < 15 && aggressionLevel > 0.7) || healthPercent < 0.3;
    }

    private void executeVanillaLikeCircling() {
        // SIMPLIFIED - Let vanilla behavior handle everything
        // Only set phase and target, no movement override

        if (primaryState == PrimaryState.VANILLA_CIRCLING || primaryState == PrimaryState.INITIALIZING) {
            // Set native circling phase for authentic behavior
            bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CIRCLING);

            // Clear target for natural circling
            if (behaviorTicks % 60 == 0) { // Every 3 seconds
                bukkitDragon.setTarget(null);
            }

            // Occasionally allow attacks (vanilla behavior)
            if (behaviorTicks % 200 == 0 && aggressionLevel > 0.5) { // Every 10 seconds
                Player target = findNearestThreat();
                if (target != null && bukkitDragon.getLocation().distance(target.getLocation()) < 60) {
                    // Transition to strafing instead of direct phase change
                    transitionToState(PrimaryState.STRAFING);
                }
            }
        }
    }

    private void executeAdvancedCircling() {
        // This method is now replaced by executeVanillaLikeCircling()
        executeVanillaLikeCircling();
    }

    private void processActionQueue() {
        while (!actionQueue.isEmpty()) {
            ComplexAction action = actionQueue.poll();
            // Execute complex action
        }
    }

    private void performLearning() {
        try {
            // Create learning sample
            Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
            double[] input = createInputVector(currentPos);
            double[] target = calculateOptimalOutput();

            // Train neural network with error handling
            if (input != null && target != null && input.length > 0 && target.length > 0) {
                neuralNetwork.backpropagate(input, target, learningRate);

                // Store in memory (only first 32 elements to match memory matrix size)
                double[] memoryInput = new double[Math.min(32, input.length)];
                System.arraycopy(input, 0, memoryInput, 0, memoryInput.length);
                memoryMatrix.store(memoryInput);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("[DRAGON AI FSM] Learning error: " + e.getMessage());
        }
    }

    private void performAdaptation() {
        if (behaviorTicks % 200 == 0) { // Every 10 seconds
            // Adapt behavior based on success
            for (int i = 0; i < adaptationMatrix.length; i++) {
                adaptationMatrix[i] += quantumRandom.nextGaussian() * adaptationRate;
                adaptationMatrix[i] = Math.max(0.1, Math.min(2.0, adaptationMatrix[i]));
            }
        }
    }

    private double[] calculateOptimalOutput() {
        // Calculate what the optimal output should be based on current situation
        return new double[]{aggressionLevel, cautionLevel, curiosityLevel, territorialLevel};
    }

    private Player findNearestThreat() {
        return bukkitDragon.getWorld().getPlayers().stream()
            .filter(this::isValidTarget)
            .filter(p -> threatProfiles.containsKey(p))
            .filter(p -> threatProfiles.get(p).shouldEngage())
            .min((p1, p2) -> Double.compare(
                bukkitDragon.getLocation().distance(p1.getLocation()),
                bukkitDragon.getLocation().distance(p2.getLocation())
            ))
            .orElse(null);
    }

    private boolean isValidTarget(Player player) {
        return player != null &&
               !player.isDead() &&
               player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
               player.getGameMode() != org.bukkit.GameMode.SPECTATOR;
    }

    private Vec3 findNearestPathfindingNode(Vec3 currentPos) {
        // Find the nearest pathfinding node for mathematical movement
        String nearestNodeName = null;
        double nearestDistance = Double.MAX_VALUE;

        for (Map.Entry<String, Vec3> entry : pathfindingNodes.entrySet()) {
            double distance = currentPos.distanceTo(entry.getValue());
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestNodeName = entry.getKey();
            }
        }

        return nearestNodeName != null ? pathfindingNodes.get(nearestNodeName) : currentPos;
    }

    private Vec3 getOptimalPathfindingTarget(String currentState) {
        // Get optimal target node based on current AI state
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());

        return switch (currentState) {
            case "HOLDING" -> {
                // Use outer ring for holding pattern
                int nodeIndex = (behaviorTicks / 20) % 12;
                yield pathfindingNodes.getOrDefault("outer_" + nodeIndex, homeVec);
            }
            case "STRAFING" -> {
                // Use inner ring for strafing
                int nodeIndex = (behaviorTicks / 15) % 8;
                yield pathfindingNodes.getOrDefault("inner_" + nodeIndex, homeVec);
            }
            case "APPROACH", "LANDING" -> {
                // Use center ring for approach/landing
                int nodeIndex = (behaviorTicks / 10) % 4;
                yield pathfindingNodes.getOrDefault("center_" + nodeIndex, homeVec);
            }
            case "PERCHING" -> pathfindingNodes.getOrDefault("fountain", homeVec);
            case "TAKEOFF" -> {
                // Move from center to inner ring
                int nodeIndex = (behaviorTicks / 20) % 8;
                yield pathfindingNodes.getOrDefault("inner_" + nodeIndex, homeVec);
            }
            case "CHARGING" -> {
                // Find node closest to target player
                Player target = findNearestThreat();
                if (target != null) {
                    Vec3 playerPos = new Vec3(target.getLocation().getX(), target.getLocation().getY(), target.getLocation().getZ());
                    yield findNearestPathfindingNode(playerPos);
                }
                yield homeVec;
            }
            default -> homeVec;
        };
    }

    private void transitionToState(PrimaryState newState) {
        if (newState != primaryState) {
            primaryState = newState;
            lastStateChange = System.currentTimeMillis();
            plugin.getLogger().info("[DRAGON AI FSM] State transition to: " + newState);
        }
    }

    private void logComplexState() {
        plugin.getLogger().info("[DRAGON AI FSM] Complex State: " +
            primaryState + "/" + secondaryState + "/" + tertiaryState + "/" + quaternaryState +
            ", Emotion: " + emotionalState + "/" + cognitiveState +
            ", Aggression: " + String.format("%.2f", aggressionLevel) +
            ", Circling: " + String.format("%.1f", circlingRadius) +
            ", Precision: " + precisionControlActive +
            ", Phase: " + bukkitDragon.getPhase());
    }

    // Mathematical Dragon AI State Handlers - Based on Minecraft Generation Research
    private void handleInitializing() {
        if (behaviorTicks > 40) transitionToState(PrimaryState.HOLDING);
    }

    private void handleHolding() {
        // HOLDING: Let vanilla circling behavior dominate with minimal AI intervention
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CIRCLING);

        // Clear target to allow natural circling
        if (behaviorTicks % 40 == 0) { // Every 2 seconds
            bukkitDragon.setTarget(null);
        }

        // Only intervene if dragon is stuck or too close to player
        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Player nearestPlayer = findNearestThreat();

        if (nearestPlayer != null) {
            double distanceToPlayer = currentPos.distanceTo(new Vec3(
                nearestPlayer.getLocation().getX(),
                nearestPlayer.getLocation().getY(),
                nearestPlayer.getLocation().getZ()
            ));

            // If too close to player, give a gentle push away
            if (distanceToPlayer < 15.0) {
                Vec3 playerPos = new Vec3(nearestPlayer.getLocation().getX(), nearestPlayer.getLocation().getY(), nearestPlayer.getLocation().getZ());
                Vec3 awayDirection = currentPos.subtract(playerPos).normalize();
                Vector pushVelocity = new Vector(
                    awayDirection.x * 0.3,
                    0.1, // Slight upward movement
                    awayDirection.z * 0.3
                );
                bukkitDragon.setVelocity(pushVelocity);
            }

            // State transition logic - more aggressive transitions
            if (aggressionLevel > 0.5 && behaviorTicks % 100 == 0) { // Every 5 seconds
                if (distanceToPlayer < 30) {
                    transitionToState(PrimaryState.STRAFING);
                }
            }
        }
    }

    private void handleStrafing() {
        // STRAFING: Use vanilla strafing behavior with target
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.STRAFING);

        Player target = findNearestThreat();
        if (target != null) {
            bukkitDragon.setTarget(target);

            // Let vanilla strafing handle the movement
            // Only add slight adjustments if needed
            Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
            Vec3 targetPos = new Vec3(target.getLocation().getX(), target.getLocation().getY(), target.getLocation().getZ());
            double distance = currentPos.distanceTo(targetPos);

            // If too far, give a gentle nudge toward target
            if (distance > 80) {
                Vec3 direction = targetPos.subtract(currentPos).normalize();
                Vector nudge = new Vector(
                    direction.x * 0.2,
                    0,
                    direction.z * 0.2
                );
                bukkitDragon.setVelocity(nudge);
            }
        } else {
            // No target, return to holding
            transitionToState(PrimaryState.HOLDING);
        }

        // Transition after strafing run
        if (behaviorTicks % 80 == 0) { // Every 4 seconds
            if (aggressionLevel > 0.8) {
                transitionToState(PrimaryState.CHARGING);
            } else {
                transitionToState(PrimaryState.HOLDING);
            }
        }
    }

    private void handleApproach() {
        // APPROACH: Move toward fountain/center gradually
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CIRCLING);

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());

        // Spiral approach pattern
        double progress = (behaviorTicks % 100) / 100.0;
        double startRadius = 80;
        double currentRadius = startRadius * (1.0 - progress * 0.7); // Approach to 30% of original radius
        double angle = behaviorTicks * 0.08;

        Vec3 targetPos = homeVec.add(
            currentRadius * Math.cos(angle),
            10,
            currentRadius * Math.sin(angle)
        );

        Vec3 direction = targetPos.subtract(currentPos);
        if (direction.length() > 2.0) {
            Vector velocity = new Vector(
                direction.x * 0.025,
                direction.y * 0.02,
                direction.z * 0.025
            );
            bukkitDragon.setVelocity(velocity);
        }

        // Transition to landing when close enough
        if (currentRadius < 25) {
            transitionToState(PrimaryState.LANDING);
        }
    }

    private void handleLanding() {
        // LANDING: Spiral down toward perch
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.LAND_ON_PORTAL);

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());

        // Spiral down pattern
        double radius = Math.max(5, 25 - (behaviorTicks % 60) * 0.3);
        double angle = behaviorTicks * 0.15;

        Vec3 targetPos = homeVec.add(
            radius * Math.cos(angle),
            Math.max(homeLocation.getY() - 5, homeLocation.getY() - (behaviorTicks % 60) * 0.2),
            radius * Math.sin(angle)
        );

        Vec3 direction = targetPos.subtract(currentPos);
        Vector velocity = new Vector(
            direction.x * 0.02,
            direction.y * 0.03,
            direction.z * 0.02
        );
        bukkitDragon.setVelocity(velocity);

        // Transition to perching when low enough
        if (currentPos.y <= homeLocation.getY() + 5) {
            transitionToState(PrimaryState.PERCHING);
        }
    }

    private void handlePerching() {
        // PERCHING: Stay near fountain with minimal movement
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.LAND_ON_PORTAL);

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());

        // Minimal movement around perch
        Vec3 targetPos = homeVec.add(
            quantumRandom.nextGaussian() * 3,
            0,
            quantumRandom.nextGaussian() * 3
        );

        Vec3 direction = targetPos.subtract(currentPos);
        if (direction.length() > 1.0) {
            Vector velocity = new Vector(
                direction.x * 0.01,
                0,
                direction.z * 0.01
            );
            bukkitDragon.setVelocity(velocity);
        }

        // Perch for a while then takeoff
        if (behaviorTicks % 120 == 0) { // Every 6 seconds
            transitionToState(PrimaryState.TAKEOFF);
        }
    }

    private void handleTakeoff() {
        // TAKEOFF: Move away from fountain in expanding spiral
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CIRCLING);

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());

        // Expanding spiral pattern
        double radius = Math.min(60, 5 + (behaviorTicks % 80) * 0.7);
        double angle = behaviorTicks * 0.1;

        Vec3 targetPos = homeVec.add(
            radius * Math.cos(angle),
            homeLocation.getY() + 10 + (behaviorTicks % 80) * 0.2,
            radius * Math.sin(angle)
        );

        Vec3 direction = targetPos.subtract(currentPos);
        Vector velocity = new Vector(
            direction.x * 0.03,
            direction.y * 0.025,
            direction.z * 0.03
        );
        bukkitDragon.setVelocity(velocity);

        // Transition back to holding when high enough
        if (radius >= 55) {
            transitionToState(PrimaryState.HOLDING);
        }
    }

    private void handleCharging() {
        // CHARGING: Use vanilla charging behavior
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CHARGE_PLAYER);

        Player target = findNearestThreat();
        if (target != null) {
            bukkitDragon.setTarget(target);

            // Let vanilla charging handle the movement
            // Vanilla CHARGE_PLAYER phase will automatically move toward target

        } else {
            // No target, return to holding
            transitionToState(PrimaryState.HOLDING);
        }

        // Timeout for charging - shorter duration
        if (behaviorTicks % 40 == 0) { // Every 2 seconds
            transitionToState(PrimaryState.HOLDING);
        }
    }

    private void handleVanillaCircling() { /* Vanilla circling logic */ }
    private void handleTerritorialPatrol() { /* Territorial patrol logic */ }
    private void handleNeuralProcessing() { /* Neural processing logic */ }
    private void handleThreatAssessment() { /* Threat assessment logic */ }
    private void handleCombatEngagement() { /* Combat engagement logic */ }
    private void handleTacticalRetreat() { /* Tactical retreat logic */ }
    private void handleLearningMode() { /* Learning mode logic */ }
    private void handleAdaptationPhase() { /* Adaptation phase logic */ }
    private void handleGeneticEvolution() { /* Genetic evolution logic */ }
    private void handleFuzzyDecision() { /* Fuzzy decision logic */ }

    // Secondary state handlers
    private void handlePassiveObservation() { /* Passive observation logic */ }
    private void handleActiveScanning() { /* Active scanning logic */ }
    private void handleThreatEvaluation() { /* Threat evaluation logic */ }
    private void handleBehaviorLearning() { /* Behavior learning logic */ }
    private void handlePatternRecognition() { /* Pattern recognition logic */ }
    private void handleDecisionOptimization() { /* Decision optimization logic */ }
    private void handleMemoryConsolidation() { /* Memory consolidation logic */ }
    private void handleSkillAdaptation() { /* Skill adaptation logic */ }
    private void handleEnvironmentalAnalysis() { /* Environmental analysis logic */ }
    private void handlePlayerProfiling() { /* Player profiling logic */ }
    private void handleCombatPreparation() { /* Combat preparation logic */ }
    private void handleStrategicPlanning() { /* Strategic planning logic */ }

    // Tertiary state handlers
    private void handleMicroAdjustments() { /* Micro adjustments logic */ }
    private void handlePrecisionControl() { /* Precision control logic */ }
    private void handleAngleOptimization() { /* Angle optimization logic */ }
    private void handleVelocityRegulation() { /* Velocity regulation logic */ }
    private void handleAltitudeManagement() { /* Altitude management logic */ }
    private void handleDirectionCalibration() { /* Direction calibration logic */ }
    private void handleMomentumControl() { /* Momentum control logic */ }
    private void handleStabilityMaintenance() { /* Stability maintenance logic */ }
    private void handleNeuralFiring() { /* Neural firing logic */ }
    private void handleSynapseAdjustment() { /* Synapse adjustment logic */ }
    private void handleWeightOptimization() { /* Weight optimization logic */ }
    private void handleGradientDescent() { /* Gradient descent logic */ }

    // Quaternary state handlers
    private void handleQuantumSuperposition() { /* Quantum superposition logic */ }
    private void handleProbabilityCollapse() { /* Probability collapse logic */ }
    private void handleEntanglementSync() { /* Entanglement sync logic */ }
    private void handleCoherenceMaintain() { /* Coherence maintain logic */ }
    private void handleGeneticMutation() { /* Genetic mutation logic */ }
    private void handleChromosomeCrossover() { /* Chromosome crossover logic */ }
    private void handleFitnessEvaluation() { /* Fitness evaluation logic */ }
    private void handleSelectionPressure() { /* Selection pressure logic */ }
    private void handleFuzzyInference() { /* Fuzzy inference logic */ }
    private void handleMembershipCalculation() { /* Membership calculation logic */ }
    private void handleRuleEvaluation() { /* Rule evaluation logic */ }
    private void handleDefuzzification() { /* Defuzzification logic */ }

    // Getters for monitoring
    public PrimaryState getPrimaryState() { return primaryState; }
    public SecondaryState getSecondaryState() { return secondaryState; }
    public EmotionalState getEmotionalState() { return emotionalState; }
    public CognitiveState getCognitiveState() { return cognitiveState; }
    public double getAggressionLevel() { return aggressionLevel; }
    public double getCirclingRadius() { return circlingRadius; }
    public boolean isPrecisionControlActive() { return precisionControlActive; }
}
