package shyrcs.dragon.dragon;

import org.bukkit.Location;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.skills.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * Custom dragon with AI and skills
 */
public class CustomDragon {
    
    private final SoulDragonPlugin plugin;
    private final EnderDragon dragon;
    private final Location homeLocation;
    private final Random random;
    private final org.bukkit.boss.BossBar bossBar;

    // Skills
    private final Map<String, DragonSkill> skills;
    private final Map<String, Long> skillCooldowns;

    // AI Task
    private BukkitTask aiTask;
    private int aiTicks;

    // AI States
    private DragonAIState currentState;
    private Player currentTarget;
    private long lastSkillUse;
    private boolean disableTargeting = false; // Flag to disable targeting when other AI is controlling

    public CustomDragon(SoulDragonPlugin plugin, EnderDragon dragon, org.bukkit.boss.BossBar bossBar) {
        this.plugin = plugin;
        this.dragon = dragon;
        this.homeLocation = dragon.getLocation().clone();
        this.random = new Random();
        this.bossBar = bossBar;

        this.skills = new HashMap<>();
        this.skillCooldowns = new HashMap<>();
        this.aiTicks = 0;
        this.currentState = DragonAIState.IDLE;
        this.lastSkillUse = 0;

        initializeSkills();
    }

    // Backward compatibility constructor
    public CustomDragon(SoulDragonPlugin plugin, EnderDragon dragon) {
        this(plugin, dragon, null);
    }
    
    /**
     * Initialize dragon skills
     */
    private void initializeSkills() {
        skills.put("rush", new RushSkill(plugin, this));
        skills.put("breath", new DragonBreathSkill(plugin, this));
        skills.put("lightning", new LightningStrikeSkill(plugin, this));
        skills.put("crystal_heal", new CrystalHealSkill(plugin, this));
        skills.put("roar", new RoarSkill(plugin, this));
        skills.put("shadow_clone", new ShadowCloneSkill(plugin, this));
    }
    
    /**
     * Start AI behavior
     */
    public void startAI() {
        if (aiTask != null && !aiTask.isCancelled()) {
            aiTask.cancel();
        }

        // Make dragon aggressive immediately
        dragon.setAI(true);

        aiTask = new BukkitRunnable() {
            @Override
            public void run() {
                if (dragon.isDead() || !dragon.isValid()) {
                    this.cancel();
                    return;
                }

                updateAI();
                aiTicks++;
            }
        }.runTaskTimer(plugin, 0L, 1L); // Run every tick

        // Start AGGRESSIVE movement task to force continuous movement
        org.bukkit.Bukkit.getScheduler().runTaskTimer(plugin, new Runnable() {
            @Override
            public void run() {
                forceDragonMovement();
            }
        }, 0L, 10L); // Every 0.5 seconds - more frequent to force movement

        // Start ANTI-STATIC task - prevent dragon from ever standing still
        org.bukkit.Bukkit.getScheduler().runTaskTimer(plugin, new Runnable() {
            @Override
            public void run() {
                preventStatic();
            }
        }, 0L, 5L); // Every 0.25 seconds - very frequent anti-static check

        SoulDragonPlugin.info("Dragon AI started with aggressive behavior");
    }

    /**
     * Prevent dragon from standing still - FORCE movement every tick
     */
    private void preventStatic() {
        if (dragon == null || dragon.isDead()) {
            return;
        }

        Location dragonLoc = dragon.getLocation();
        org.bukkit.util.Vector currentVel = dragon.getVelocity();
        double currentSpeed = currentVel.length();

        // If dragon is moving too slowly, FORCE movement
        if (currentSpeed < 0.3) {
            org.bukkit.util.Vector forceVel;

            if (currentTarget != null && !currentTarget.isDead()) {
                // Force movement towards target
                forceVel = currentTarget.getLocation().toVector()
                    .subtract(dragonLoc.toVector())
                    .normalize()
                    .multiply(1.8);
                forceVel.setY(Math.abs(forceVel.getY()) + 0.4);
            } else {
                // Force random movement
                forceVel = new org.bukkit.util.Vector(
                    (random.nextDouble() - 0.5) * 2.0,
                    0.3 + random.nextDouble() * 0.5,
                    (random.nextDouble() - 0.5) * 2.0
                );
            }

            dragon.setVelocity(forceVel);
        }

        // Always add small random movement to prevent perfect stillness
        if (aiTicks % 10 == 0) { // Every 0.5 seconds
            org.bukkit.util.Vector microMovement = new org.bukkit.util.Vector(
                (random.nextDouble() - 0.5) * 0.4,
                (random.nextDouble() - 0.5) * 0.2,
                (random.nextDouble() - 0.5) * 0.4
            );
            dragon.setVelocity(dragon.getVelocity().add(microMovement));
        }

        // Force height maintenance
        if (dragonLoc.getY() < homeLocation.getY() + 3) {
            org.bukkit.util.Vector upward = new org.bukkit.util.Vector(0, 0.8, 0);
            dragon.setVelocity(dragon.getVelocity().add(upward));
        }

        // Prevent flying too high
        if (dragonLoc.getY() > homeLocation.getY() + 50) {
            org.bukkit.util.Vector downward = new org.bukkit.util.Vector(0, -0.5, 0);
            dragon.setVelocity(dragon.getVelocity().add(downward));
        }
    }
    
    /**
     * Update AI behavior - more natural approach
     */
    private void updateAI() {
        // Update boss bar
        updateBossBar();

        // Skip ALL AI behavior if targeting is disabled (other AI is controlling)
        if (disableTargeting) {
            // Only handle boss bar updates, no movement or targeting
            if (aiTicks % 100 == 0) { // Log every 5 seconds
                plugin.getLogger().info("[CUSTOM AI] Targeting disabled - only boss bar active");
            }
            return;
        }

        // Keep dragon in designated area (less restrictive)
        enforceAreaRestriction();

        // Natural target finding
        updateTarget();

        // Natural state transitions
        if (currentTarget != null && !currentTarget.isDead()) {
            if (currentState != DragonAIState.COMBAT) {
                currentState = DragonAIState.COMBAT;
                dragon.setTarget(currentTarget);
            }
        } else {
            if (currentState != DragonAIState.IDLE) {
                currentState = DragonAIState.IDLE;
            }
        }

        // Execute AI based on current state
        switch (currentState) {
            case IDLE -> handleIdleState();
            case COMBAT -> handleCombatState();
            case SKILL_CASTING -> handleSkillCastingState();
        }

        // Use skills naturally
        if (aiTicks % 60 == 0) { // Every 3 seconds
            considerUsingSkill();
        }

        // Less frequent aggressive checks to allow natural behavior
        if (aiTicks % 40 == 0) { // Every 2 seconds
            ensureTargeting();
        }
    }

    /**
     * Ensure dragon has proper targeting (less aggressive than makeAggressive)
     */
    private void ensureTargeting() {
        // Ensure dragon AI is enabled
        dragon.setAI(true);

        // Only find new target if current one is invalid
        if (currentTarget == null || currentTarget.isDead()) {
            for (Player player : dragon.getWorld().getPlayers()) {
                if (player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
                    player.getGameMode() != org.bukkit.GameMode.SPECTATOR &&
                    !player.isDead()) {

                    double distance = dragon.getLocation().distance(player.getLocation());
                    if (distance < 80) { // Reasonable detection range
                        dragon.setTarget(player);
                        currentTarget = player;
                        currentState = DragonAIState.COMBAT;
                        break;
                    }
                }
            }
        }

        // Maintain current target if valid
        if (currentTarget != null && !currentTarget.isDead()) {
            dragon.setTarget(currentTarget);

            // Occasional roar (less frequent)
            if (random.nextInt(200) < 1) { // 0.5% chance
                dragon.getWorld().playSound(dragon.getLocation(),
                    org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 1.0f);
            }
        }
    }
    
    /**
     * Update boss bar
     */
    private void updateBossBar() {
        if (bossBar != null) {
            double maxHealth = dragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
            double healthPercentage = dragon.getHealth() / maxHealth;
            bossBar.setProgress(Math.max(0.0, Math.min(1.0, healthPercentage)));

            // Add new players who joined the world
            for (Player player : dragon.getWorld().getPlayers()) {
                if (!bossBar.getPlayers().contains(player)) {
                    bossBar.addPlayer(player);
                }
            }
        }
    }

    /**
     * FORCE dragon movement - NO TELEPORT, ONLY VELOCITY
     */
    private void forceDragonMovement() {
        Location dragonLoc = dragon.getLocation();

        // Always find and attack nearest player
        updateTarget();

        // Get current velocity to check if dragon is moving
        org.bukkit.util.Vector currentVel = dragon.getVelocity();
        double currentSpeed = currentVel.length();

        if (currentTarget != null && !currentTarget.isDead()) {
            // Force dragon to target the player
            dragon.setTarget(currentTarget);

            Location targetLoc = currentTarget.getLocation();
            double distance = dragonLoc.distance(targetLoc);

            // ALWAYS apply velocity towards target - NO TELEPORT
            org.bukkit.util.Vector direction = targetLoc.toVector()
                .subtract(dragonLoc.toVector())
                .normalize();

            // Adjust velocity based on distance
            double velocityMultiplier;
            if (distance > 30) {
                velocityMultiplier = 2.5; // Very fast for long distance
            } else if (distance > 15) {
                velocityMultiplier = 2.0; // Fast for medium distance
            } else {
                velocityMultiplier = 1.5; // Moderate for close distance
            }

            // Add height component for natural flight
            direction.setY(direction.getY() + 0.4);

            // Apply strong velocity
            dragon.setVelocity(direction.multiply(velocityMultiplier));

            // Ensure dragon stays airborne
            if (dragonLoc.getY() < targetLoc.getY() + 3) {
                org.bukkit.util.Vector upward = new org.bukkit.util.Vector(0, 1.0, 0);
                dragon.setVelocity(dragon.getVelocity().add(upward));
            }

        } else {
            // No target - FORCE patrol movement
            for (Player player : dragon.getWorld().getPlayers()) {
                if (player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
                    player.getGameMode() != org.bukkit.GameMode.SPECTATOR &&
                    !player.isDead()) {

                    double playerDistance = dragonLoc.distance(player.getLocation());
                    if (playerDistance < 100) {
                        // Found a player, FORCE movement towards them
                        dragon.setTarget(player);
                        currentTarget = player;

                        // STRONG velocity towards player
                        org.bukkit.util.Vector direction = player.getLocation().toVector()
                            .subtract(dragonLoc.toVector())
                            .normalize()
                            .multiply(2.0); // Strong approach
                        direction.setY(direction.getY() + 0.5);
                        dragon.setVelocity(direction);
                        return;
                    }
                }
            }

            // No players found - FORCE patrol movement
            double homeDistance = dragonLoc.distance(homeLocation);

            if (homeDistance > 50) {
                // FORCE return to home
                org.bukkit.util.Vector homeDirection = homeLocation.toVector()
                    .subtract(dragonLoc.toVector())
                    .normalize()
                    .multiply(1.5); // Strong return velocity
                homeDirection.setY(0.4);
                dragon.setVelocity(homeDirection);
            } else {
                // FORCE random patrol movement
                org.bukkit.util.Vector randomDirection = new org.bukkit.util.Vector(
                    (random.nextDouble() - 0.5) * 2.0, // Stronger random movement
                    (random.nextDouble() - 0.5) * 0.8 + 0.3,
                    (random.nextDouble() - 0.5) * 2.0
                );
                dragon.setVelocity(randomDirection);
            }
        }

        // FORCE minimum movement if dragon is too slow
        if (currentSpeed < 0.5) {
            org.bukkit.util.Vector forceMovement = new org.bukkit.util.Vector(
                (random.nextDouble() - 0.5) * 1.5,
                0.5,
                (random.nextDouble() - 0.5) * 1.5
            );
            dragon.setVelocity(forceMovement);
        }

        // FORCE height maintenance
        if (dragonLoc.getY() < homeLocation.getY() + 5) {
            org.bukkit.util.Vector upward = new org.bukkit.util.Vector(0, 1.2, 0);
            dragon.setVelocity(dragon.getVelocity().add(upward));
        }
    }

    /**
     * Keep dragon within designated area
     */
    private void enforceAreaRestriction() {
        double maxDistance = 100.0; // Maximum distance from home

        if (dragon.getLocation().distance(homeLocation) > maxDistance) {
            // Teleport dragon back towards home
            Location targetLocation = homeLocation.clone().add(
                random.nextDouble() * 20 - 10,
                random.nextDouble() * 10,
                random.nextDouble() * 20 - 10
            );

            dragon.teleport(targetLocation);
        }
    }
    
    /**
     * Update current target
     */
    private void updateTarget() {
        // Skip targeting if disabled (other AI is controlling)
        if (disableTargeting) {
            return;
        }

        Player nearestPlayer = null;
        double nearestDistance = Double.MAX_VALUE;
        double maxRange = 80.0; // Increased range

        for (Player player : dragon.getWorld().getPlayers()) {
            if (player.getGameMode() == org.bukkit.GameMode.CREATIVE ||
                player.getGameMode() == org.bukkit.GameMode.SPECTATOR) {
                continue;
            }

            // Skip dead players
            if (player.isDead() || player.getHealth() <= 0) {
                continue;
            }

            double distance = player.getLocation().distance(dragon.getLocation());
            if (distance < maxRange && distance < nearestDistance) {
                nearestDistance = distance;
                nearestPlayer = player;
            }
        }

        // Keep current target if still valid and close
        if (currentTarget != null && !currentTarget.isDead() &&
            currentTarget.getGameMode() != org.bukkit.GameMode.CREATIVE &&
            currentTarget.getGameMode() != org.bukkit.GameMode.SPECTATOR) {

            double currentDistance = dragon.getLocation().distance(currentTarget.getLocation());
            if (currentDistance <= maxRange * 1.2) { // Give some leeway to current target
                // Keep current target unless new one is much closer
                if (nearestPlayer == null || nearestDistance > currentDistance * 0.7) {
                    nearestPlayer = currentTarget;
                }
            }
        }

        currentTarget = nearestPlayer;

        // Update AI state based on target
        if (currentTarget != null) {
            if (currentState == DragonAIState.IDLE) {
                currentState = DragonAIState.COMBAT;
            }
        } else {
            if (currentState == DragonAIState.COMBAT) {
                currentState = DragonAIState.IDLE;
            }
        }
    }
    
    /**
     * Handle idle state - FORCE continuous movement
     */
    private void handleIdleState() {
        Location dragonLoc = dragon.getLocation();

        // Search for players naturally
        for (Player player : dragon.getWorld().getPlayers()) {
            if (player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
                player.getGameMode() != org.bukkit.GameMode.SPECTATOR &&
                !player.isDead()) {

                double distance = dragonLoc.distance(player.getLocation());
                if (distance < 80) { // Reasonable detection radius
                    // Found a target, switch to combat naturally
                    currentTarget = player;
                    dragon.setTarget(player);
                    currentState = DragonAIState.COMBAT;

                    // FORCE immediate movement towards player
                    org.bukkit.util.Vector approachVector = player.getLocation().toVector()
                        .subtract(dragonLoc.toVector())
                        .normalize()
                        .multiply(2.0); // Stronger velocity
                    approachVector.setY(Math.abs(approachVector.getY()) + 0.5);
                    dragon.setVelocity(approachVector);

                    // Roar when spotting target
                    dragon.getWorld().playSound(dragonLoc,
                        org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 3.0f, 0.9f);

                    return; // Exit idle state
                }
            }
        }

        // FORCE continuous movement - NEVER let dragon stop
        double homeDistance = dragonLoc.distance(homeLocation);

        // Always apply some velocity to prevent standing still
        org.bukkit.util.Vector currentVelocity = dragon.getVelocity();
        double currentSpeed = currentVelocity.length();

        // If dragon is moving too slowly, FORCE movement
        if (currentSpeed < 0.3) {
            org.bukkit.util.Vector forceVector;

            if (homeDistance > 60) {
                // Return to home area with STRONG velocity
                forceVector = homeLocation.toVector()
                    .subtract(dragonLoc.toVector())
                    .normalize()
                    .multiply(1.5); // Strong return velocity
                forceVector.setY(0.4);
            } else {
                // Create FORCED patrol movement
                double angle = (System.currentTimeMillis() / 1000.0) * 0.5; // Time-based rotation
                double patrolRadius = 25;

                Location patrolTarget = homeLocation.clone().add(
                    Math.cos(angle) * patrolRadius,
                    15 + Math.sin(angle * 1.5) * 8, // More dramatic height changes
                    Math.sin(angle) * patrolRadius
                );

                forceVector = patrolTarget.toVector()
                    .subtract(dragonLoc.toVector())
                    .normalize()
                    .multiply(1.2); // Strong patrol velocity

                // Add randomness to prevent predictable movement
                forceVector.add(new org.bukkit.util.Vector(
                    (random.nextDouble() - 0.5) * 0.5,
                    (random.nextDouble() - 0.5) * 0.3,
                    (random.nextDouble() - 0.5) * 0.5
                ));
            }

            // FORCE apply the velocity
            dragon.setVelocity(forceVector);
        }

        // Additional movement every tick to ensure continuous motion
        if (aiTicks % 5 == 0) { // Every 0.25 seconds
            org.bukkit.util.Vector additionalMovement = new org.bukkit.util.Vector(
                (random.nextDouble() - 0.5) * 0.3,
                (random.nextDouble() - 0.5) * 0.2,
                (random.nextDouble() - 0.5) * 0.3
            );
            dragon.setVelocity(dragon.getVelocity().add(additionalMovement));
        }

        // FORCE minimum flight height
        if (dragonLoc.getY() < homeLocation.getY() + 8) {
            org.bukkit.util.Vector upward = new org.bukkit.util.Vector(0, 0.8, 0);
            dragon.setVelocity(dragon.getVelocity().add(upward));
        }

        // Prevent dragon from flying too high
        if (dragonLoc.getY() > homeLocation.getY() + 40) {
            org.bukkit.util.Vector downward = new org.bukkit.util.Vector(0, -0.3, 0);
            dragon.setVelocity(dragon.getVelocity().add(downward));
        }

        // Occasional ambient sounds while patrolling
        if (aiTicks % 200 == 0) { // Every 10 seconds
            dragon.getWorld().playSound(dragonLoc,
                org.bukkit.Sound.ENTITY_ENDER_DRAGON_AMBIENT, 1.5f, 1.0f);
        }

        // Wing flap particles for natural effect
        if (aiTicks % 30 == 0) { // Every 1.5 seconds
            dragon.getWorld().spawnParticle(
                org.bukkit.Particle.CLOUD,
                dragonLoc.clone().add(0, -2, 0),
                5, 3.0, 1.0, 3.0, 0.1
            );
        }
    }
    
    /**
     * Handle combat state
     */
    private void handleCombatState() {
        if (currentTarget == null || currentTarget.isDead()) {
            currentState = DragonAIState.IDLE;
            return;
        }

        // Skip movement control if targeting is disabled (other AI is controlling)
        if (disableTargeting) {
            // Only handle skills, no movement or targeting
            considerUsingSkill();
            return;
        }

        // Force dragon to target the player using natural targeting
        dragon.setTarget(currentTarget);

        Location dragonLoc = dragon.getLocation();
        Location targetLoc = currentTarget.getLocation();
        double distance = dragonLoc.distance(targetLoc);

        // Natural dragon combat movement - no teleporting
        if (distance > 10) {
            // Calculate natural attack approach
            org.bukkit.util.Vector attackVector = targetLoc.toVector()
                .subtract(dragonLoc.toVector())
                .normalize();

            // Add height for diving attack
            attackVector.setY(attackVector.getY() + 0.2);

            // Apply velocity for natural movement
            dragon.setVelocity(attackVector.multiply(1.8)); // Fast attack speed

        } else if (distance < 5) {
            // Too close, natural retreat
            org.bukkit.util.Vector retreatVector = dragonLoc.toVector()
                .subtract(targetLoc.toVector())
                .normalize();
            retreatVector.setY(0.5); // Fly upward while retreating
            dragon.setVelocity(retreatVector.multiply(1.0));
        }

        // Melee attack - when dragon is close
        if (distance < 15.0 && aiTicks % 25 == 0) { // Every 1.25 seconds
            // Natural dragon swipe attack
            for (Player player : dragon.getNearbyEntities(18, 18, 18).stream()
                    .filter(entity -> entity instanceof Player)
                    .map(entity -> (Player) entity)
                    .toList()) {

                if (player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
                    player.getGameMode() != org.bukkit.GameMode.SPECTATOR &&
                    !player.isDead()) {

                    // Dragon claw damage
                    player.damage(6.0, dragon);

                    // Natural knockback from dragon wing/claw
                    org.bukkit.util.Vector knockback = player.getLocation().toVector()
                        .subtract(dragonLoc.toVector()).normalize().multiply(1.5);
                    knockback.setY(0.6); // Natural upward force
                    player.setVelocity(knockback);

                    // Wing flap particles
                    player.getWorld().spawnParticle(
                        org.bukkit.Particle.CLOUD,
                        dragonLoc,
                        10, 2.0, 1.0, 2.0, 0.1
                    );
                }
            }

            // Dragon roar
            dragon.getWorld().playSound(dragonLoc, org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 2.5f, 1.0f);
        }

        // Fireball attack for ranged combat
        if (distance > 20.0 && distance < 50.0 && aiTicks % 50 == 0) { // Every 2.5 seconds
            try {
                // Natural fireball from dragon mouth
                org.bukkit.util.Vector direction = targetLoc.toVector().subtract(dragonLoc.toVector()).normalize();
                Location fireballSpawn = dragonLoc.clone().add(direction.multiply(3));
                fireballSpawn.add(0, -2, 0); // Spawn from dragon's mouth level

                // Launch fireball
                org.bukkit.entity.Fireball fireball = dragon.getWorld().spawn(fireballSpawn, org.bukkit.entity.Fireball.class);
                fireball.setDirection(direction);
                fireball.setYield(2.5f); // Moderate explosion
                fireball.setShooter(dragon);

                // Dragon fire sound
                dragon.getWorld().playSound(dragonLoc, org.bukkit.Sound.ENTITY_BLAZE_SHOOT, 1.5f, 0.8f);

            } catch (Exception e) {
                // Fallback: direct magic damage
                if (distance < 40) {
                    currentTarget.damage(3.0, dragon);
                    currentTarget.getWorld().spawnParticle(
                        org.bukkit.Particle.DRAGON_BREATH,
                        currentTarget.getLocation(),
                        5, 1.0, 1.0, 1.0, 0.1
                    );
                }
            }
        }

        // Dragon breath attack for close range
        if (distance < 15.0 && aiTicks % 80 == 0) { // Every 4 seconds
            // Natural dragon breath
            org.bukkit.entity.AreaEffectCloud cloud = dragon.getWorld().spawn(
                targetLoc, org.bukkit.entity.AreaEffectCloud.class
            );
            cloud.setParticle(org.bukkit.Particle.DRAGON_BREATH);
            cloud.setRadius(4.0f);
            cloud.setDuration(80); // 4 seconds
            cloud.addCustomEffect(new org.bukkit.potion.PotionEffect(
                org.bukkit.potion.PotionEffectType.INSTANT_DAMAGE, 1, 0
            ), true);

            // Dragon breath sound
            dragon.getWorld().playSound(dragonLoc, org.bukkit.Sound.ENTITY_ENDER_DRAGON_SHOOT, 1.8f, 1.0f);
        }

        // Ensure dragon stays airborne during combat
        if (dragonLoc.getY() < targetLoc.getY() + 3) {
            org.bukkit.util.Vector upward = new org.bukkit.util.Vector(0, 0.6, 0);
            dragon.setVelocity(dragon.getVelocity().add(upward));
        }
    }
    
    /**
     * Handle skill casting state
     */
    private void handleSkillCastingState() {
        // This state is managed by individual skills
        // Return to combat state after a short time
        if (System.currentTimeMillis() - lastSkillUse > 3000) {
            currentState = DragonAIState.COMBAT;
        }
    }
    
    /**
     * Consider using a skill
     */
    private void considerUsingSkill() {
        if (currentTarget == null || currentState == DragonAIState.SKILL_CASTING) {
            return;
        }
        
        // Check health percentage for different skill priorities
        double maxHealth = dragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue();
        double healthPercentage = dragon.getHealth() / maxHealth;
        
        String skillToUse = null;
        
        if (healthPercentage < 0.3) {
            // Low health - prioritize healing and defensive skills
            if (isSkillReady("crystal_heal")) {
                skillToUse = "crystal_heal";
            } else if (isSkillReady("roar")) {
                skillToUse = "roar";
            }
        } else if (healthPercentage < 0.6) {
            // Medium health - use all skills
            String[] allSkills = {"rush", "breath", "lightning", "roar", "shadow_clone"};
            for (String skill : allSkills) {
                if (isSkillReady(skill) && random.nextDouble() < 0.3) {
                    skillToUse = skill;
                    break;
                }
            }
        } else {
            // High health - use offensive skills
            String[] offensiveSkills = {"rush", "breath", "lightning"};
            for (String skill : offensiveSkills) {
                if (isSkillReady(skill) && random.nextDouble() < 0.2) {
                    skillToUse = skill;
                    break;
                }
            }
        }
        
        if (skillToUse != null) {
            useSkill(skillToUse);
        }
    }
    
    /**
     * Check if skill is ready (not on cooldown)
     */
    private boolean isSkillReady(String skillName) {
        Long lastUse = skillCooldowns.get(skillName);
        if (lastUse == null) {
            return true;
        }
        
        DragonSkill skill = skills.get(skillName);
        if (skill == null) {
            return false;
        }
        
        long cooldownMs = skill.getCooldownSeconds() * 1000L;
        return System.currentTimeMillis() - lastUse >= cooldownMs;
    }
    
    /**
     * Use a skill
     */
    public void useSkill(String skillName) {
        DragonSkill skill = skills.get(skillName);
        if (skill == null || !isSkillReady(skillName)) {
            return;
        }
        
        if (skill.canUse()) {
            skill.use();
            skillCooldowns.put(skillName, System.currentTimeMillis());
            lastSkillUse = System.currentTimeMillis();
            currentState = DragonAIState.SKILL_CASTING;
            
            SoulDragonPlugin.info("Dragon used skill: " + skillName);
        }
    }
    
    /**
     * Get the dragon entity
     */
    public EnderDragon getDragon() {
        return dragon;
    }
    
    /**
     * Get current target
     */
    public Player getCurrentTarget() {
        return currentTarget;
    }
    
    /**
     * Get home location
     */
    public Location getHomeLocation() {
        return homeLocation.clone();
    }

    /**
     * Disable targeting - for use when other AI systems are controlling movement
     */
    public void setTargetingDisabled(boolean disabled) {
        this.disableTargeting = disabled;
        if (disabled) {
            // Clear current target when disabling
            this.currentTarget = null;
        }
    }
    
    /**
     * Cleanup dragon AI and skills
     */
    public void cleanup() {
        if (aiTask != null && !aiTask.isCancelled()) {
            aiTask.cancel();
        }

        // Remove boss bar
        if (bossBar != null) {
            bossBar.removeAll();
        }

        for (DragonSkill skill : skills.values()) {
            skill.cleanup();
        }
    }
    
    /**
     * Dragon AI states
     */
    public enum DragonAIState {
        IDLE,
        COMBAT,
        SKILL_CASTING
    }
}
