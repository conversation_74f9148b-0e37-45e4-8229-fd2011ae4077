package shyrcs.dragon.listeners;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import shyrcs.dragon.SoulDragonPlugin;

/**
 * Listener for setup item interactions
 */
public class SetupItemListener implements Listener {
    
    private final SoulDragonPlugin plugin;
    
    public SetupItemListener(SoulDragonPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        Block clickedBlock = event.getClickedBlock();
        
        if (item == null || clickedBlock == null) {
            return;
        }
        
        // Check if player has permission
        if (!player.hasPermission("souldragon.setup")) {
            return;
        }
        
        // Check if it's a setup item
        if (!plugin.getSetupManager().isSetupItem(item)) {
            return;
        }
        
        String setupType = plugin.getSetupManager().getSetupType(item);
        if (setupType == null) {
            return;
        }
        
        // Cancel the event to prevent normal block interaction
        event.setCancelled(true);
        
        // Handle different setup types
        switch (setupType) {
            case "boss_position" -> handleBossPositionSetup(player, item, clickedBlock);
            case "altar" -> handleAltarSetup(player, item, clickedBlock);
            case "crystal" -> handleCrystalSetup(player, item, clickedBlock);
        }
    }
    
    /**
     * Handle boss position setup
     */
    private void handleBossPositionSetup(Player player, ItemStack item, Block block) {
        int required = plugin.getSetupManager().getRequiredCount("boss_position");
        
        if (item.getAmount() < required) {
            String message = plugin.getConfig().getString("messages.setup.insufficient_items", 
                "§c✗ Không đủ Que Quỷ Lửa!");
            player.sendMessage(message);
            return;
        }
        
        Location location = block.getLocation();
        
        // Save to database
        if (plugin.getDatabase().saveBossPosition(location)) {
            // Remove items
            if (item.getAmount() == required) {
                player.getInventory().setItemInMainHand(null);
            } else {
                item.setAmount(item.getAmount() - required);
            }
            
            String message = plugin.getConfig().getString("messages.setup.boss_position_set", 
                "§a✓ Đã đánh dấu vị trí triệu hồi boss!");
            player.sendMessage(message);
            
            SoulDragonPlugin.info("Boss position set at " + formatLocation(location) + " by " + player.getName());
        } else {
            player.sendMessage("§c✗ Lỗi khi lưu vị trí boss!");
        }
    }
    
    /**
     * Handle altar setup
     */
    private void handleAltarSetup(Player player, ItemStack item, Block block) {
        // Check if block is End Portal Frame
        if (block.getType() != Material.END_PORTAL_FRAME) {
            String message = plugin.getConfig().getString("messages.setup.invalid_block",
                "§c✗ Chỉ có thể đặt tế đàn trên End Portal Frame!");
            player.sendMessage(message);
            return;
        }

        // Check if boss position is set up first
        if (plugin.getDatabase().getBossPositions().isEmpty()) {
            player.sendMessage("§c✗ Cần setup vị trí boss trước khi tạo tế đàn!");
            return;
        }

        // Check if this altar is within 3x3 range of boss position
        if (!isWithinBossArea(block.getLocation(), 3)) {
            player.sendMessage("§c✗ Tế đàn phải nằm trong vùng 3x3 xung quanh vị trí boss!");
            return;
        }

        int required = 1; // Chỉ cần 1 que cho mỗi End Portal

        if (item.getAmount() < required) {
            String message = plugin.getConfig().getString("messages.setup.insufficient_items",
                "§c✗ Không đủ Que Quỷ Lửa!");
            player.sendMessage(message);
            return;
        }

        Location location = block.getLocation();

        // Check if we already have 8 altars
        if (plugin.getDatabase().getAltarPositions().size() >= 8) {
            player.sendMessage("§c✗ Đã có đủ 8 tế đàn! Không thể tạo thêm.");
            return;
        }

        // Save to database
        if (plugin.getDatabase().saveAltarPosition(location)) {
            // Remove items
            if (item.getAmount() == required) {
                player.getInventory().setItemInMainHand(null);
            } else {
                item.setAmount(item.getAmount() - required);
            }

            int currentAltars = plugin.getDatabase().getAltarPositions().size();
            String message = plugin.getConfig().getString("messages.setup.altar_created",
                "§a✓ Đã tạo tế đàn triệu hồi! ({current}/8)")
                .replace("{current}", String.valueOf(currentAltars));
            player.sendMessage(message);

            SoulDragonPlugin.info("Altar created at " + formatLocation(location) + " by " + player.getName() +
                " (" + currentAltars + "/8)");
        } else {
            player.sendMessage("§c✗ Lỗi khi lưu vị trí tế đàn!");
        }
    }
    
    /**
     * Handle crystal setup
     */
    private void handleCrystalSetup(Player player, ItemStack item, Block block) {
        // Check if boss position is set up first
        if (plugin.getDatabase().getBossPositions().isEmpty()) {
            player.sendMessage("§c✗ Cần setup vị trí boss trước khi đặt End Crystal!");
            return;
        }

        // Check if this crystal is within 200x200x200 range of boss position
        if (!isWithinBossArea(block.getLocation(), 100)) {
            player.sendMessage("§c✗ End Crystal phải nằm trong vùng 200x200x200 xung quanh vị trí boss!");
            return;
        }

        int required = 1; // Chỉ cần 1 que cho mỗi vị trí crystal

        if (item.getAmount() < required) {
            String message = plugin.getConfig().getString("messages.setup.insufficient_items",
                "§c✗ Không đủ Que Quỷ Lửa!");
            player.sendMessage(message);
            return;
        }

        Location location = block.getLocation();

        // Check if we already have 12 crystal positions
        if (plugin.getDatabase().getCrystalPositions().size() >= 12) {
            player.sendMessage("§c✗ Đã có đủ 12 vị trí End Crystal! Không thể tạo thêm.");
            return;
        }

        // Save to database
        if (plugin.getDatabase().saveCrystalPosition(location)) {
            // Remove items
            if (item.getAmount() == required) {
                player.getInventory().setItemInMainHand(null);
            } else {
                item.setAmount(item.getAmount() - required);
            }

            int currentCrystals = plugin.getDatabase().getCrystalPositions().size();
            String message = plugin.getConfig().getString("messages.setup.crystal_position_set",
                "§a✓ Đã đánh dấu vị trí End Crystal! ({current}/12)")
                .replace("{current}", String.valueOf(currentCrystals));
            player.sendMessage(message);

            SoulDragonPlugin.info("Crystal position set at " + formatLocation(location) + " by " + player.getName() +
                " (" + currentCrystals + "/12)");
        } else {
            player.sendMessage("§c✗ Lỗi khi lưu vị trí crystal!");
        }
    }
    
    /**
     * Check if location is within boss area
     */
    private boolean isWithinBossArea(Location location, int radius) {
        for (var bossPos : plugin.getDatabase().getBossPositions()) {
            Location bossLocation = bossPos.toLocation();
            if (bossLocation != null && bossLocation.getWorld().equals(location.getWorld())) {
                double distance = location.distance(bossLocation);
                if (distance <= radius) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Format location for logging
     */
    private String formatLocation(Location loc) {
        return String.format("%s: %.1f, %.1f, %.1f",
            loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }
}
