name: SoulDragonCustom
version: 1.0.0
author: Shyrcs
main: shyrcs.dragon.SoulDragonPlugin
description: Custom Dragon Battle System with setup items and boss mechanics
api-version: 1.21
depend: []
soft-depend: [HeadDatabase, MythicMobs]

commands:
  sdc:
    description: Soul Dragon Custom main command
    usage: /sdc <subcommand>
    aliases: [souldragon, dragonbattle]
    permission: souldragon.admin
  dragontest:
    description: Dragon AI testing commands
    usage: /dragontest [spawn|ai|combat|cleanup|info]
    permission: souldragon.test

permissions:
  souldragon.admin:
    description: Admin permissions for Soul Dragon Custom
    default: op
  souldragon.setup:
    description: Permission to use setup items
    default: op
  souldragon.summon:
    description: Permission to summon the dragon
    default: true
  souldragon.test:
    description: Access to dragon testing commands
    default: op
