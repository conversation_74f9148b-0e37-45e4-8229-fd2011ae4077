package shyrcs.dragon.dragon.skills;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.EnderCrystal;
import org.bukkit.entity.EntityType;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Crystal Heal skill - Spawns End Crystals that heal the dragon
 */
public class CrystalHealSkill extends DragonSkill {
    
    private final Random random = new Random();
    private final List<EnderCrystal> healingCrystals = new ArrayList<>();
    
    public CrystalHealSkill(SoulDragonPlugin plugin, CustomDragon customDragon) {
        super(plugin, customDragon, "crystal_heal");
    }
    
    @Override
    public boolean canUse() {
        // Only use when dragon health is below 70%
        double healthPercentage = customDragon.getDragon().getHealth() / customDragon.getDragon().getMaxHealth();
        return healthPercentage < 0.7 && healingCrystals.isEmpty();
    }
    
    @Override
    public void use() {
        Location dragonLocation = customDragon.getDragon().getLocation();
        int crystalCount = plugin.getConfig().getInt("dragon.skills.crystal_heal.crystal_count", 6);
        
        // Play sound
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 1.5f);
        
        // Spawn crystals around the dragon
        for (int i = 0; i < crystalCount; i++) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    spawnHealingCrystal();
                }
            }.runTaskLater(plugin, i * 10L); // Spawn one every 0.5 seconds
        }
        
        // Start healing process
        startHealingProcess();
    }
    
    /**
     * Spawn a healing crystal around the dragon
     */
    private void spawnHealingCrystal() {
        Location dragonLocation = customDragon.getDragon().getLocation();
        
        // Calculate random position around dragon
        double angle = random.nextDouble() * 2 * Math.PI;
        double distance = 10 + random.nextDouble() * 10; // 10-20 blocks away
        double height = random.nextDouble() * 10 - 5; // -5 to +5 blocks height variation
        
        Location crystalLocation = dragonLocation.clone().add(
            Math.cos(angle) * distance,
            height,
            Math.sin(angle) * distance
        );
        
        // Ensure crystal is above ground
        while (crystalLocation.getBlock().getType().isSolid() && crystalLocation.getY() < dragonLocation.getY() + 20) {
            crystalLocation.add(0, 1, 0);
        }
        
        try {
            EnderCrystal crystal = (EnderCrystal) crystalLocation.getWorld().spawnEntity(crystalLocation, EntityType.END_CRYSTAL);
            crystal.setShowingBottom(false); // No bedrock base
            
            healingCrystals.add(crystal);
            
            // Particle effects
            crystalLocation.getWorld().spawnParticle(
                Particle.PORTAL,
                crystalLocation,
                20,
                1.0, 1.0, 1.0,
                0.1
            );
            
            crystalLocation.getWorld().playSound(crystalLocation, Sound.BLOCK_GLASS_PLACE, 1.0f, 1.0f);
            
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to spawn healing crystal: " + e.getMessage());
        }
    }
    
    /**
     * Start the healing process
     */
    private void startHealingProcess() {
        int healDuration = plugin.getConfig().getInt("dragon.skills.crystal_heal.heal_duration", 10);
        double healAmount = plugin.getConfig().getDouble("dragon.skills.crystal_heal.heal_amount", 200.0);
        
        new BukkitRunnable() {
            private int ticks = 0;
            private final int maxTicks = healDuration * 20; // Convert seconds to ticks
            
            @Override
            public void run() {
                ticks++;
                
                // Remove destroyed crystals from list
                healingCrystals.removeIf(crystal -> crystal.isDead() || !crystal.isValid());
                
                if (ticks >= maxTicks || healingCrystals.isEmpty()) {
                    // Healing period ended or all crystals destroyed
                    destroyRemainingCrystals();
                    this.cancel();
                    return;
                }
                
                // Heal dragon every second if crystals are alive
                if (ticks % 20 == 0 && !healingCrystals.isEmpty()) {
                    double healPerSecond = healAmount / healDuration;
                    double currentHealth = customDragon.getDragon().getHealth();
                    double maxHealth = customDragon.getDragon().getMaxHealth();
                    
                    double newHealth = Math.min(maxHealth, currentHealth + healPerSecond);
                    customDragon.getDragon().setHealth(newHealth);
                    
                    // Healing particles
                    Location dragonLocation = customDragon.getDragon().getLocation();
                    dragonLocation.getWorld().spawnParticle(
                        Particle.HEART,
                        dragonLocation,
                        5,
                        2.0, 2.0, 2.0,
                        0.1
                    );
                    
                    // Beam effects from crystals to dragon
                    for (EnderCrystal crystal : healingCrystals) {
                        if (!crystal.isDead() && crystal.isValid()) {
                            createHealingBeam(crystal.getLocation(), dragonLocation);
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }
    
    /**
     * Create healing beam effect from crystal to dragon
     */
    private void createHealingBeam(Location crystalLocation, Location dragonLocation) {
        // Create particle line from crystal to dragon
        double distance = crystalLocation.distance(dragonLocation);
        int particleCount = (int) (distance * 2);
        
        for (int i = 0; i < particleCount; i++) {
            double progress = (double) i / particleCount;
            Location particleLocation = crystalLocation.clone().add(
                dragonLocation.clone().subtract(crystalLocation).multiply(progress)
            );
            
            particleLocation.getWorld().spawnParticle(
                Particle.END_ROD,
                particleLocation,
                1,
                0.1, 0.1, 0.1,
                0.0
            );
        }
    }
    
    /**
     * Destroy remaining crystals
     */
    private void destroyRemainingCrystals() {
        for (EnderCrystal crystal : healingCrystals) {
            if (!crystal.isDead() && crystal.isValid()) {
                crystal.remove();
                
                // Explosion effect
                Location crystalLocation = crystal.getLocation();
                crystalLocation.getWorld().spawnParticle(
                    Particle.EXPLOSION,
                    crystalLocation,
                    3,
                    1.0, 1.0, 1.0,
                    0.0
                );
            }
        }
        healingCrystals.clear();
    }
    
    @Override
    public int getCooldownSeconds() {
        return 60; // Long cooldown for powerful healing skill
    }
    
    @Override
    public void cleanup() {
        destroyRemainingCrystals();
    }
}
