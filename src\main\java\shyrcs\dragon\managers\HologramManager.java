package shyrcs.dragon.managers;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.dragon.SoulDragonPlugin;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Manager for hologram heads on altars
 */
public class HologramManager {
    
    private final SoulDragonPlugin plugin;
    private final Map<Location, EnderEyeHologram> holograms;
    
    public HologramManager(SoulDragonPlugin plugin) {
        this.plugin = plugin;
        this.holograms = new HashMap<>();
    }
    
    /**
     * Create hologram at altar location
     */
    public boolean createHologram(Location altarLocation) {
        if (holograms.containsKey(altarLocation)) {
            return false; // Already exists
        }
        
        EnderEyeHologram hologram = new EnderEyeHologram(plugin, altarLocation);
        if (hologram.isValid()) {
            holograms.put(altarLocation, hologram);
            return true;
        }
        
        return false;
    }
    
    /**
     * Remove hologram at location
     */
    public boolean removeHologram(Location altarLocation) {
        EnderEyeHologram hologram = holograms.remove(altarLocation);
        if (hologram != null) {
            hologram.remove();
            return true;
        }
        return false;
    }
    
    /**
     * Get hologram count at altar
     */
    public int getHologramCount(Location altarLocation) {
        EnderEyeHologram hologram = holograms.get(altarLocation);
        return hologram != null ? hologram.getEyeCount() : 0;
    }
    
    /**
     * Add eye to hologram
     */
    public boolean addEyeToHologram(Location altarLocation) {
        EnderEyeHologram hologram = holograms.get(altarLocation);
        if (hologram != null) {
            return hologram.addEye();
        }
        return false;
    }
    
    /**
     * Check if altar is complete (1 eye per altar)
     */
    public boolean isAltarComplete(Location altarLocation) {
        return getHologramCount(altarLocation) >= 1;
    }
    
    /**
     * Remove all holograms at altar
     */
    public void removeAllHologramsAt(Location altarLocation) {
        EnderEyeHologram hologram = holograms.remove(altarLocation);
        if (hologram != null) {
            hologram.removeAll();
        }
    }
    
    /**
     * Cleanup all holograms
     */
    public void cleanup() {
        for (EnderEyeHologram hologram : holograms.values()) {
            hologram.removeAll();
        }
        holograms.clear();
    }
    
    /**
     * Class representing an Ender Eye hologram on an altar
     */
    public static class EnderEyeHologram {
        
        private final SoulDragonPlugin plugin;
        private final Location altarLocation;
        private final Map<Integer, ArmorStand> eyeHolograms;
        private final Map<Integer, BukkitTask> rotationTasks;
        private int eyeCount;
        
        public EnderEyeHologram(SoulDragonPlugin plugin, Location altarLocation) {
            this.plugin = plugin;
            this.altarLocation = altarLocation.clone();
            this.eyeHolograms = new HashMap<>();
            this.rotationTasks = new HashMap<>();
            this.eyeCount = 0;
        }
        
        /**
         * Add an eye to the altar (max 1 per altar)
         */
        public boolean addEye() {
            if (eyeCount >= 1) {
                return false; // Already has an eye
            }

            Location eyeLocation = calculateEyePosition(eyeCount);
            ArmorStand hologram = createEyeHologram(eyeLocation);

            if (hologram != null) {
                eyeHolograms.put(eyeCount, hologram);
                startRotationTask(eyeCount, hologram);
                eyeCount++;
                return true;
            }

            return false;
        }
        
        /**
         * Calculate position for eye based on index
         */
        private Location calculateEyePosition(int index) {
            // Stack eyes vertically in the center of the block
            double baseHeight = 1.0; // Start 1 block above the altar
            double stackHeight = 0.3; // Height between each eye

            // Center the hologram on the block (0.5, 0.5 offset)
            return altarLocation.clone().add(0.5, baseHeight + (index * stackHeight), 0.5);
        }
        
        /**
         * Create hologram armor stand with custom head
         */
        private ArmorStand createEyeHologram(Location location) {
            try {
                ArmorStand hologram = (ArmorStand) location.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
                
                // Configure armor stand
                hologram.setVisible(false);
                hologram.setGravity(false);
                hologram.setCanPickupItems(false);
                hologram.setRemoveWhenFarAway(false);
                hologram.setInvulnerable(true);
                hologram.setMarker(true);
                hologram.setSmall(false);
                hologram.setBasePlate(false);
                hologram.setArms(false);
                
                // Create custom head
                ItemStack customHead = createEnderEyeHead();
                hologram.getEquipment().setHelmet(customHead);
                
                return hologram;
                
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to create ender eye hologram: " + e.getMessage());
                return null;
            }
        }
        
        /**
         * Create custom ender eye head
         */
        private ItemStack createEnderEyeHead() {
            try {
                // Try to use custom texture from config
                String texture = plugin.getConfig().getString("hologram.ender_eye.texture");

                if (texture != null && !texture.isEmpty()) {
                    ItemStack customHead = createHeadFromBase64(texture);
                    if (customHead != null) {
                        return customHead;
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to create head with custom texture: " + e.getMessage());
            }
            
            // Fallback: use ender eye item
            return new ItemStack(Material.ENDER_EYE);
        }
        
        /**
         * Create head from base64 using Paper API
         */
        private ItemStack createHeadFromBase64(String base64) {
            try {
                ItemStack head = new ItemStack(Material.PLAYER_HEAD);
                SkullMeta meta = (SkullMeta) head.getItemMeta();
                
                if (meta != null) {
                    // Create PlayerProfile using Paper API
                    com.destroystokyo.paper.profile.PlayerProfile profile =
                        Bukkit.createProfile(UUID.randomUUID(), "EnderEye");
                    
                    // Create texture property
                    com.destroystokyo.paper.profile.ProfileProperty property =
                        new com.destroystokyo.paper.profile.ProfileProperty("textures", base64);
                    
                    // Set texture property
                    profile.setProperty(property);
                    
                    // Use Paper API method to set profile directly
                    meta.setPlayerProfile(profile);
                    head.setItemMeta(meta);
                    
                    return head;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to create head from base64: " + e.getMessage());
            }
            return null;
        }
        
        /**
         * Start rotation task for hologram
         */
        private void startRotationTask(int index, ArmorStand hologram) {
            double rotationSpeed = plugin.getConfig().getDouble("hologram.ender_eye.rotation_speed", 5.0);
            
            BukkitTask task = new BukkitRunnable() {
                private float yaw = 0.0f;
                
                @Override
                public void run() {
                    if (hologram == null || hologram.isDead()) {
                        this.cancel();
                        return;
                    }
                    
                    // Increase yaw for rotation effect
                    yaw += rotationSpeed;
                    if (yaw >= 360.0f) {
                        yaw = 0.0f;
                    }
                    
                    // Create new location with updated yaw
                    Location rotatedLocation = hologram.getLocation().clone();
                    rotatedLocation.setYaw(yaw);
                    
                    // Teleport hologram with new rotation
                    hologram.teleport(rotatedLocation);
                }
            }.runTaskTimer(plugin, 0L, 2L); // Update every 2 ticks for smooth rotation
            
            rotationTasks.put(index, task);
        }
        
        /**
         * Get current eye count
         */
        public int getEyeCount() {
            return eyeCount;
        }
        
        /**
         * Check if hologram is valid
         */
        public boolean isValid() {
            return altarLocation != null && altarLocation.getWorld() != null;
        }
        
        /**
         * Remove specific eye
         */
        public void remove() {
            if (eyeCount > 0) {
                int lastIndex = eyeCount - 1;
                
                // Cancel rotation task
                BukkitTask task = rotationTasks.remove(lastIndex);
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
                
                // Remove hologram
                ArmorStand hologram = eyeHolograms.remove(lastIndex);
                if (hologram != null && !hologram.isDead()) {
                    hologram.remove();
                }
                
                eyeCount--;
            }
        }
        
        /**
         * Remove all eyes
         */
        public void removeAll() {
            // Cancel all rotation tasks
            for (BukkitTask task : rotationTasks.values()) {
                if (task != null && !task.isCancelled()) {
                    task.cancel();
                }
            }
            rotationTasks.clear();
            
            // Remove all holograms
            for (ArmorStand hologram : eyeHolograms.values()) {
                if (hologram != null && !hologram.isDead()) {
                    hologram.remove();
                }
            }
            eyeHolograms.clear();
            
            eyeCount = 0;
        }
    }
}
