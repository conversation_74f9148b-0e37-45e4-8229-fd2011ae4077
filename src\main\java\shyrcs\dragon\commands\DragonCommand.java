package shyrcs.dragon.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import shyrcs.dragon.SoulDragonPlugin;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Main command handler for Soul Dragon Custom
 */
public class DragonCommand implements CommandExecutor, TabCompleter {
    
    private final SoulDragonPlugin plugin;
    
    public DragonCommand(SoulDragonPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            sendHelp(sender);
            return true;
        }
        
        String subCommand = args[0].toLowerCase();
        
        switch (subCommand) {
            case "setup" -> {
                return handleSetup(sender);
            }
            case "remove" -> {
                if (args.length >= 2 && args[1].equalsIgnoreCase("all")) {
                    return handleRemoveAll(sender);
                } else {
                    sender.sendMessage("§c✗ Sử dụng: /sdc remove all");
                }
            }
            case "give" -> {
                if (args.length >= 2) {
                    return handleGive(sender, args);
                } else {
                    sender.sendMessage("§c✗ Sử dụng: /sdc give <item_type> [player]");
                }
            }
            case "reload" -> {
                return handleReload(sender);
            }
            case "info" -> {
                return handleInfo(sender);
            }
            default -> sendHelp(sender);
        }
        
        return true;
    }
    
    /**
     * Handle setup GUI command
     */
    private boolean handleSetup(CommandSender sender) {
        if (!(sender instanceof Player player)) {
            sender.sendMessage("§c✗ Lệnh này chỉ dành cho player!");
            return true;
        }

        if (!player.hasPermission("souldragon.setup")) {
            String message = plugin.getConfig().getString("messages.admin.no_permission",
                "§c✗ Bạn không có quyền sử dụng lệnh này!");
            player.sendMessage(message);
            return true;
        }

        plugin.getSetupGUI().openSetupGUI(player);
        return true;
    }

    /**
     * Handle remove all command
     */
    private boolean handleRemoveAll(CommandSender sender) {
        if (!sender.hasPermission("souldragon.admin")) {
            String message = plugin.getConfig().getString("messages.admin.no_permission", 
                "§c✗ Bạn không có quyền sử dụng lệnh này!");
            sender.sendMessage(message);
            return true;
        }
        
        if (plugin.getDatabase().removeAllSetupData()) {
            String message = plugin.getConfig().getString("messages.admin.all_removed", 
                "§a✓ Đã xóa tất cả đánh dấu setup!");
            sender.sendMessage(message);
            
            SoulDragonPlugin.info("All setup data removed by " + sender.getName());
        } else {
            sender.sendMessage("§c✗ Lỗi khi xóa dữ liệu setup!");
        }
        
        return true;
    }
    
    /**
     * Handle give command
     */
    private boolean handleGive(CommandSender sender, String[] args) {
        if (!sender.hasPermission("souldragon.admin")) {
            String message = plugin.getConfig().getString("messages.admin.no_permission", 
                "§c✗ Bạn không có quyền sử dụng lệnh này!");
            sender.sendMessage(message);
            return true;
        }
        
        String itemType = args[1].toLowerCase();
        Player target;
        
        if (args.length >= 3) {
            target = plugin.getServer().getPlayer(args[2]);
            if (target == null) {
                sender.sendMessage("§c✗ Không tìm thấy player: " + args[2]);
                return true;
            }
        } else {
            if (!(sender instanceof Player)) {
                sender.sendMessage("§c✗ Bạn phải chỉ định player khi sử dụng từ console!");
                return true;
            }
            target = (Player) sender;
        }
        
        ItemStack item = switch (itemType) {
            case "boss", "boss_position" -> plugin.getSetupManager().createBossPositionItem();
            case "altar" -> plugin.getSetupManager().createAltarItem();
            case "crystal" -> plugin.getSetupManager().createCrystalItem();
            case "eye", "ender_eye" -> plugin.getSetupManager().createEnderEyeItem();
            default -> null;
        };
        
        if (item == null) {
            sender.sendMessage("§c✗ Item type không hợp lệ! Sử dụng: boss, altar, crystal, eye");
            return true;
        }
        
        target.getInventory().addItem(item);
        sender.sendMessage("§a✓ Đã đưa " + itemType + " cho " + target.getName());
        
        if (!sender.equals(target)) {
            target.sendMessage("§a✓ Bạn đã nhận được " + itemType + " từ " + sender.getName());
        }
        
        return true;
    }
    
    /**
     * Handle reload command
     */
    private boolean handleReload(CommandSender sender) {
        if (!sender.hasPermission("souldragon.admin")) {
            String message = plugin.getConfig().getString("messages.admin.no_permission", 
                "§c✗ Bạn không có quyền sử dụng lệnh này!");
            sender.sendMessage(message);
            return true;
        }
        
        plugin.reloadConfig();
        sender.sendMessage("§a✓ Đã reload config thành công!");
        
        SoulDragonPlugin.info("Config reloaded by " + sender.getName());
        return true;
    }
    
    /**
     * Handle info command
     */
    private boolean handleInfo(CommandSender sender) {
        if (!sender.hasPermission("souldragon.admin")) {
            String message = plugin.getConfig().getString("messages.admin.no_permission", 
                "§c✗ Bạn không có quyền sử dụng lệnh này!");
            sender.sendMessage(message);
            return true;
        }
        
        int bossPositions = plugin.getDatabase().getBossPositions().size();
        int altarPositions = plugin.getDatabase().getAltarPositions().size();
        int crystalPositions = plugin.getDatabase().getCrystalPositions().size();
        
        sender.sendMessage("§6=== Soul Dragon Custom Info ===");
        sender.sendMessage("§eBoss Positions: §f" + bossPositions);
        sender.sendMessage("§eAltar Positions: §f" + altarPositions);
        sender.sendMessage("§eCrystal Positions: §f" + crystalPositions);
        sender.sendMessage("§6===============================");
        
        return true;
    }
    
    /**
     * Send help message
     */
    private void sendHelp(CommandSender sender) {
        sender.sendMessage("§6=== Soul Dragon Custom Commands ===");
        sender.sendMessage("§e/sdc setup §7- Mở GUI lấy setup items nhanh");
        sender.sendMessage("§e/sdc remove all §7- Xóa tất cả setup data");
        sender.sendMessage("§e/sdc give <type> [player] §7- Đưa setup items");
        sender.sendMessage("§e/sdc reload §7- Reload config");
        sender.sendMessage("§e/sdc info §7- Hiển thị thông tin setup");
        sender.sendMessage("§7Item types: boss, altar, crystal, eye");
        sender.sendMessage("§6===================================");
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 1) {
            List<String> subCommands = Arrays.asList("setup", "remove", "give", "reload", "info");
            for (String subCommand : subCommands) {
                if (subCommand.toLowerCase().startsWith(args[0].toLowerCase())) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2) {
            if (args[0].equalsIgnoreCase("remove")) {
                if ("all".startsWith(args[1].toLowerCase())) {
                    completions.add("all");
                }
            } else if (args[0].equalsIgnoreCase("give")) {
                List<String> itemTypes = Arrays.asList("boss", "altar", "crystal", "eye");
                for (String itemType : itemTypes) {
                    if (itemType.startsWith(args[1].toLowerCase())) {
                        completions.add(itemType);
                    }
                }
            }
        } else if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
            // Player names
            for (Player player : plugin.getServer().getOnlinePlayers()) {
                if (player.getName().toLowerCase().startsWith(args[2].toLowerCase())) {
                    completions.add(player.getName());
                }
            }
        }
        
        return completions;
    }
}
