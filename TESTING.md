# SoulDragonCustom Testing Checklist

## Pre-Testing Setup

### Server Requirements
- [ ] Paper/Spigot 1.21+ server running
- [ ] Java 17+ installed
- [ ] Plugin compiled successfully
- [ ] Plugin loaded without errors

### Optional Dependencies
- [ ] HeadDatabase plugin (for custom textures)
- [ ] MythicMobs plugin (for enhanced effects)

## Basic Functionality Tests

### 1. Plugin Loading
- [ ] Plugin loads without errors
- [ ] Config file generates correctly
- [ ] Database file creates successfully
- [ ] Commands register properly

### 2. Setup Items
- [ ] `/sdc give boss` creates boss position item
- [ ] `/sdc give altar` creates altar setup item  
- [ ] `/sdc give crystal` creates crystal setup item
- [ ] `/sdc give eye` creates Ender Eye item
- [ ] Items have correct names and lore
- [ ] Items have proper NBT tags

### 3. Setup Process
- [ ] Boss position item works on right-click
- [ ] Altar item only works on End Portal Frames
- [ ] Crystal item works on any block
- [ ] Items are consumed after use
- [ ] Success messages display correctly
- [ ] Database saves locations properly

## Advanced Functionality Tests

### 4. Summoning System
- [ ] Ender Eyes only work on registered altars
- [ ] Hologram heads appear when placing eyes
- [ ] Holograms rotate correctly
- [ ] 8 eyes required to start summoning
- [ ] Summoning sequence starts automatically

### 5. Summoning Sequence
- [ ] Poem lines display in chat
- [ ] End Crystals spawn at marked locations
- [ ] 12-second timing works correctly
- [ ] Dragon spawns with explosion
- [ ] Holograms disappear after summoning

### 6. Dragon Boss
- [ ] Dragon has correct name "Rồng Ngàn Năm"
- [ ] Health is 2000 HP
- [ ] White boss bar appears
- [ ] Boss bar updates with health
- [ ] Dragon AI activates

### 7. Dragon Skills
- [ ] **Rush**: Dragon dives and damages players
- [ ] **Dragon's Breath**: Fire projectiles with knockup
- [ ] **Lightning Strike**: Lightning targets players
- [ ] **Crystal Heal**: Healing crystals spawn
- [ ] **Roar**: Area debuffs and knockback
- [ ] **Shadow Clones**: Illusion clones appear

## Edge Case Tests

### 8. Error Handling
- [ ] Invalid block types handled gracefully
- [ ] Insufficient items show proper messages
- [ ] Missing permissions block actions
- [ ] Database errors don't crash plugin
- [ ] Invalid commands show help

### 9. Cleanup Tests
- [ ] Plugin disable cleans up entities
- [ ] Dragon death removes boss bar
- [ ] Holograms cleanup on server restart
- [ ] Database connections close properly

### 10. Performance Tests
- [ ] Multiple dragons don't cause lag
- [ ] Particle effects don't overwhelm server
- [ ] AI tasks don't consume excessive CPU
- [ ] Memory usage remains stable

## Integration Tests

### 11. Multi-Player Testing
- [ ] Multiple players can see dragon
- [ ] Boss bar visible to all players
- [ ] Skills affect multiple targets
- [ ] Summoning works with multiple players

### 12. World Compatibility
- [ ] Works in different world types
- [ ] Handles world unloading gracefully
- [ ] Cross-world functionality works
- [ ] Respects world borders

## Command Tests

### 13. Admin Commands
- [ ] `/sdc remove all` clears database
- [ ] `/sdc reload` reloads config
- [ ] `/sdc info` shows statistics
- [ ] Tab completion works
- [ ] Permission checks work

### 14. Configuration Tests
- [ ] Config changes apply on reload
- [ ] Invalid config values handled
- [ ] Default values work correctly
- [ ] Custom messages display properly

## Final Validation

### 15. Complete Battle Test
- [ ] Full setup process works
- [ ] Summoning ritual completes
- [ ] Dragon spawns and fights
- [ ] All skills activate during battle
- [ ] Dragon death handled properly
- [ ] Cleanup occurs after battle

### 16. Stress Testing
- [ ] Multiple simultaneous summons
- [ ] Rapid skill usage
- [ ] Large number of crystals
- [ ] Extended battle duration

## Bug Reporting Template

When reporting bugs, include:
- Server version and type
- Plugin version
- Steps to reproduce
- Expected vs actual behavior
- Console errors (if any)
- Configuration settings

## Performance Benchmarks

Monitor these metrics during testing:
- TPS (should remain above 19.5)
- Memory usage
- Entity count
- Particle count
- Database query time

## Success Criteria

The plugin passes testing if:
- [ ] All basic functionality works
- [ ] No critical errors in console
- [ ] Performance remains acceptable
- [ ] Complete dragon battle possible
- [ ] Cleanup works properly
