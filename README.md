# SoulDragonCustom - Dragon Battle System

A comprehensive custom dragon battle system for Minecraft servers with setup items, summoning rituals, and advanced boss mechanics.

## Features

### Setup System
- **Boss Position Setup**: Use 1 Fire Rod to mark boss spawn location (130 blocks above)
- **Altar Setup**: Use 8 Fire Rods on End Portal Frame to create summoning altar
- **Crystal Setup**: Use 12 Fire Rods to mark End Crystal spawn positions

### Summoning Ritual
- Place 8 custom Ender Eyes on altars to start summoning
- 12-second ritual with End Crystal spawning and epic poem
- Dramatic dragon spawn with explosion effects

### Custom Dragon Boss: "Rồng Ngàn Năm"
- **Health**: 2000 HP with white boss bar
- **AI**: Stays in designated area, intelligent targeting
- **Skills**:
  - **Rush**: Dive attack with area damage
  - **Dragon's Breath**: Fire projectiles with knockup
  - **Lightning Strike**: Unavoidable lightning attacks
  - **Crystal Heal**: Spawns healing crystals
  - **Roar**: Area debuffs and knockback
  - **Shadow Clones**: Deceptive illusions

## Installation

1. Place the plugin JAR in your server's `plugins` folder
2. Restart the server
3. Configure the plugin in `plugins/SoulDragonCustom/config.yml`

## Dependencies

- **Required**: Paper/Spigot 1.21+
- **Optional**: HeadDatabase (for custom textures)
- **Optional**: MythicMobs (for enhanced effects)

## Commands

### Admin Commands
- `/sdc setup` - **Mở GUI lấy setup items nhanh** ⭐
- `/sdc give <type> [player]` - Give setup items
  - Types: `boss`, `altar`, `crystal`, `eye`
- `/sdc remove all` - Remove all setup data
- `/sdc reload` - Reload configuration
- `/sdc info` - Show setup statistics

### Setup Items
- **Boss Position**: `/sdc give boss` - Marks dragon spawn location
- **Altar**: `/sdc give altar` - Creates summoning altar (End Portal Frame only)
- **Crystal**: `/sdc give crystal` - Marks End Crystal positions
- **Ender Eye**: `/sdc give eye` - For summoning ritual

## Permissions

- `souldragon.admin` - Access to all commands (default: op)
- `souldragon.setup` - Use setup items (default: op)
- `souldragon.summon` - Summon dragon (default: true)

## Setup Guide

### 🎮 Quick Setup (Recommended)
1. Use `/sdc setup` to open the Setup GUI
2. Click on items to get them instantly:
   - **Boss Position** - 1 Fire Rod to mark spawn location
   - **Altar Setup** - 8 Fire Rods for summoning altar
   - **Crystal Setup** - 12 Fire Rods for End Crystal positions
   - **Ender Eyes** - 8 eyes for summoning ritual

### 📋 Manual Setup (Alternative)
1. **Mark Boss Spawn Location**
   - Get boss position item: `/sdc give boss`
   - Right-click on any block where you want the dragon to spawn
   - Dragon will spawn 130 blocks above this location

2. **Create Summoning Altar**
   - Build End Portal Frame structure
   - Get altar item: `/sdc give altar`
   - Right-click on End Portal Frame blocks to register them as altars

3. **Mark Crystal Positions**
   - Get crystal item: `/sdc give crystal`
   - Right-click on blocks where End Crystals should spawn during summoning
   - Recommend 12+ positions around the battle area

4. **Test Summoning**
   - Get Ender Eyes: `/sdc give eye`
   - Right-click on registered altar blocks with Ender Eyes
   - Place all 8 eyes to start the summoning ritual

## Configuration

Key configuration options in `config.yml`:

```yaml
dragon:
  name: "§f§lRồng Ngàn Năm"
  health: 2000.0
  
summoning:
  poem: # Custom poem lines
  crystal_spawn_interval: 1
  
setup_items:
  boss_position:
    cost: 1
  altar:
    cost: 8
  crystal:
    cost: 12
```

## Troubleshooting

### Common Issues

1. **Dragon not spawning**
   - Ensure boss position is set
   - Check that altar is properly registered
   - Verify all 8 Ender Eyes are placed

2. **Setup items not working**
   - Check permissions (`souldragon.setup`)
   - Ensure sufficient items in inventory
   - For altars, only End Portal Frames work

3. **Database errors**
   - Check file permissions in plugin folder
   - Restart server if database is corrupted

### Debug Commands
- `/sdc info` - Check setup statistics
- Check console for detailed error messages

## Technical Details

### Database
- SQLite database stores all setup locations
- Automatic cleanup on plugin disable
- Persistent across server restarts

### Performance
- Optimized AI with configurable tick rates
- Efficient particle systems
- Automatic cleanup of temporary entities

### Compatibility
- Paper/Spigot 1.21+
- Java 17+
- Compatible with most plugins

## Support

For issues or questions:
1. Check console logs for errors
2. Verify configuration syntax
3. Test with minimal plugin setup
4. Report bugs with full error logs

## Version History

### v1.0.0
- Initial release
- Complete dragon battle system
- All skills implemented
- Setup system functional
