package shyrcs.dragon.commands;

import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.Player;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

public class TestCommand implements CommandExecutor {
    
    private final SoulDragonPlugin plugin;
    
    public TestCommand(SoulDragonPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c✗ Chỉ player mới có thể sử dụng lệnh này!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (!player.hasPermission("souldragon.test")) {
            player.sendMessage("§c✗ Bạn không có quyền sử dụng lệnh này!");
            return true;
        }
        
        if (args.length == 0) {
            sendTestHelp(player);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "spawn" -> handleSpawnTest(player);
            case "ai" -> handleAITest(player);
            case "combat" -> handleCombatTest(player);
            case "cleanup" -> handleCleanup(player);
            case "info" -> handleInfo(player);
            case "scenario" -> {
                if (args.length >= 2 && args[1].equalsIgnoreCase("mathematical")) {
                    handleMathematicalScenarioTest(player);
                } else {
                    handleScenarioTest(player);
                }
            }
            case "movement" -> handleMovementTest(player);
            default -> sendTestHelp(player);
        }
        
        return true;
    }
    
    private void sendTestHelp(Player player) {
        player.sendMessage("§6§l=== DRAGON TEST COMMANDS ===");
        player.sendMessage("§e/dragontest spawn §7- Spawn test dragon tại vị trí hiện tại");
        player.sendMessage("§e/dragontest ai §7- Test dragon AI và movement");
        player.sendMessage("§e/dragontest combat §7- Test dragon combat abilities");
        player.sendMessage("§e/dragontest scenario §7- Chạy test scenario tự động");
        player.sendMessage("§e/dragontest scenario mathematical §7- Test Mathematical AI system");
        player.sendMessage("§e/dragontest movement §7- Test natural movement (NEW)");
        player.sendMessage("§e/dragontest cleanup §7- Remove tất cả test dragons");
        player.sendMessage("§e/dragontest info §7- Hiển thị thông tin dragon AI");
    }
    
    private void handleSpawnTest(Player player) {
        Location spawnLoc = player.getLocation().add(0, 20, 0);

        String spawnMsg = "✓ Spawning test dragon for player: " + player.getName() +
            " at " + String.format("%.1f, %.1f, %.1f", spawnLoc.getX(), spawnLoc.getY(), spawnLoc.getZ());
        player.sendMessage("§a" + spawnMsg);
        plugin.getLogger().info("[DRAGON TEST] " + spawnMsg);

        // Spawn dragon
        EnderDragon dragon = (EnderDragon) spawnLoc.getWorld().spawnEntity(spawnLoc, EntityType.ENDER_DRAGON);

        // Configure dragon
        dragon.customName(net.kyori.adventure.text.Component.text("§c§lTEST DRAGON"));
        dragon.setCustomNameVisible(true);
        dragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).setBaseValue(1000.0);
        dragon.setHealth(1000.0);
        dragon.setAI(true);

        String configMsg = "Dragon configured: Health=1000, AI=true, Name=TEST DRAGON";
        plugin.getLogger().info("[DRAGON TEST] " + configMsg);

        // Create boss bar
        org.bukkit.boss.BossBar bossBar = org.bukkit.Bukkit.createBossBar(
            "§c§lTEST DRAGON",
            org.bukkit.boss.BarColor.RED,
            org.bukkit.boss.BarStyle.SOLID
        );
        bossBar.addPlayer(player);

        plugin.getLogger().info("[DRAGON TEST] Boss bar created and player added");

        // Start AI after delay
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            // Start ULTIMATE DRAGON AI FSM (NMS + FSM + Neural Network + Quantum Logic)
            shyrcs.dragon.dragon.DragonAIFSM dragonAIFSM =
                new shyrcs.dragon.dragon.DragonAIFSM(plugin, dragon);
            dragonAIFSM.startAI();

            // Start Custom AI ONLY for boss bar and combat features (no movement control)
            CustomDragon customDragon = new CustomDragon(plugin, dragon, bossBar);
            customDragon.startAI();

            // Let Dragon AI FSM handle all targeting and movement with ultimate intelligence

            String aiMsg = "✓ Test dragon spawned với ULTIMATE DRAGON AI FSM! NMS + FSM + Neural Network + Quantum Logic.";
            String cleanupMsg = "⚠ Sử dụng /dragontest cleanup để remove dragon.";

            player.sendMessage("§a" + aiMsg);
            player.sendMessage("§e" + cleanupMsg);

            plugin.getLogger().info("[DRAGON TEST] " + aiMsg);
            plugin.getLogger().info("[DRAGON TEST] ULTIMATE AI SYSTEM: Dragon AI FSM + Custom AI (boss bar only)");

        }, 10L);
    }
    
    private void handleAITest(Player player) {
        // Find nearest dragon
        EnderDragon nearestDragon = null;
        double nearestDistance = Double.MAX_VALUE;
        
        for (EnderDragon dragon : player.getWorld().getEntitiesByClass(EnderDragon.class)) {
            double distance = dragon.getLocation().distance(player.getLocation());
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestDragon = dragon;
            }
        }
        
        if (nearestDragon == null) {
            player.sendMessage("§c✗ Không tìm thấy dragon nào! Sử dụng /dragontest spawn trước.");
            return;
        }
        
        // Force dragon to target player
        nearestDragon.setTarget(player);
        nearestDragon.setAI(true);
        
        player.sendMessage("§a✓ Dragon AI test started!");
        player.sendMessage("§e📊 Dragon Stats:");
        player.sendMessage("§7- Distance: §f" + String.format("%.1f", nearestDistance) + " blocks");
        player.sendMessage("§7- Health: §f" + nearestDragon.getHealth() + "/" + nearestDragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue());
        player.sendMessage("§7- Has Target: §f" + (nearestDragon.getTarget() != null));
        player.sendMessage("§7- AI Enabled: §f" + nearestDragon.hasAI());
        
        // Test movement
        Location targetLoc = player.getLocation().add(0, 15, 0);
        nearestDragon.teleport(targetLoc);
        
        player.sendMessage("§e⚡ Dragon được teleport gần bạn để test AI!");
    }
    
    private void handleCombatTest(Player player) {
        // Find nearest dragon
        final EnderDragon[] nearestDragonRef = {null};
        for (EnderDragon dragon : player.getWorld().getEntitiesByClass(EnderDragon.class)) {
            double distance = dragon.getLocation().distance(player.getLocation());
            if (distance < 50) {
                nearestDragonRef[0] = dragon;
                break;
            }
        }

        if (nearestDragonRef[0] == null) {
            player.sendMessage("§c✗ Không tìm thấy dragon gần đó! Sử dụng /dragontest spawn trước.");
            return;
        }

        player.sendMessage("§c⚔ COMBAT TEST STARTED!");
        player.sendMessage("§e⚠ Dragon sẽ tấn công bạn trong 3 giây...");

        // Countdown
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§c3...");
        }, 20L);

        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§c2...");
        }, 40L);

        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§c1...");
        }, 60L);

        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            player.sendMessage("§4§lCOMBAT!");

            // Force aggressive behavior
            nearestDragonRef[0].setTarget(player);
            nearestDragonRef[0].setAI(true);

            // Move dragon close for combat
            Location combatLoc = player.getLocation().add(0, 10, 0);
            nearestDragonRef[0].teleport(combatLoc);

            // Play sound
            player.getWorld().playSound(nearestDragonRef[0].getLocation(),
                org.bukkit.Sound.ENTITY_ENDER_DRAGON_GROWL, 3.0f, 0.8f);

        }, 80L);
    }

    private void handleScenarioTest(Player player) {
        player.sendMessage("§6🧪 Starting automated Dragon AI test scenario...");
        player.sendMessage("§e⚠ This will take about 30 seconds to complete.");
        player.sendMessage("§7The test will automatically spawn a dragon and test all AI features.");

        // Start the automated test scenario
        shyrcs.dragon.test.DragonAITestScenario scenario =
            new shyrcs.dragon.test.DragonAITestScenario(plugin, player);
        scenario.startTest();
    }

    private void handleMathematicalScenarioTest(Player player) {
        player.sendMessage("§6🧮 Starting Mathematical Dragon AI test scenario...");
        player.sendMessage("§e⚠ This will test the pure mathematical AI system.");
        player.sendMessage("§7Based on Minecraft Generation Research algorithms.");

        // Start the mathematical test scenario
        shyrcs.dragon.test.DragonAITestScenario scenario =
            new shyrcs.dragon.test.DragonAITestScenario(plugin, player);
        scenario.startMathematicalTest();
    }

    private void handleMovementTest(Player player) {
        // Find nearest dragon
        EnderDragon nearestDragon = null;
        double nearestDistance = Double.MAX_VALUE;

        for (EnderDragon dragon : player.getWorld().getEntitiesByClass(EnderDragon.class)) {
            double distance = dragon.getLocation().distance(player.getLocation());
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestDragon = dragon;
            }
        }

        if (nearestDragon == null) {
            String msg = "✗ Không tìm thấy dragon nào! Sử dụng /dragontest spawn trước.";
            player.sendMessage("§c" + msg);
            plugin.getLogger().info("[DRAGON TEST] " + msg);
            return;
        }

        String testStart = "🔄 NATURAL MOVEMENT TEST - Player: " + player.getName();
        player.sendMessage("§a" + testStart);
        plugin.getLogger().info("[DRAGON TEST] " + testStart);

        String testInfo = "📊 Testing natural dragon flight behavior...";
        player.sendMessage("§e" + testInfo);
        plugin.getLogger().info("[DRAGON TEST] " + testInfo);

        final EnderDragon testDragon = nearestDragon;
        final Location startPos = testDragon.getLocation().clone();

        // Clear target to test idle movement
        testDragon.setTarget(null);

        String phase1 = "Phase 1: Testing idle patrol behavior (10 seconds)";
        player.sendMessage("§7" + phase1);
        plugin.getLogger().info("[DRAGON TEST] " + phase1);

        // Test idle movement for 10 seconds
        org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
            Location afterIdlePos = testDragon.getLocation();
            double idleDistance = startPos.distance(afterIdlePos);

            String idleResult = "Idle movement distance: " + String.format("%.1f", idleDistance) + " blocks";
            player.sendMessage("§7" + idleResult);
            plugin.getLogger().info("[DRAGON TEST] " + idleResult);

            String idleStatus;
            if (idleDistance > 5) {
                idleStatus = "✓ Dragon shows natural idle movement";
                player.sendMessage("§a" + idleStatus);
            } else {
                idleStatus = "✗ Dragon barely moved during idle";
                player.sendMessage("§c" + idleStatus);
            }
            plugin.getLogger().info("[DRAGON TEST] " + idleStatus);

            // Phase 2: Test targeting movement
            String phase2 = "Phase 2: Testing combat approach behavior (5 seconds)";
            player.sendMessage("§7" + phase2);
            plugin.getLogger().info("[DRAGON TEST] " + phase2);
            testDragon.setTarget(player);

            final Location combatStartPos = testDragon.getLocation().clone();

            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                Location afterCombatPos = testDragon.getLocation();
                double combatDistance = combatStartPos.distance(afterCombatPos);
                double playerDistance = afterCombatPos.distance(player.getLocation());

                String combatResult = "Combat movement distance: " + String.format("%.1f", combatDistance) + " blocks";
                player.sendMessage("§7" + combatResult);
                plugin.getLogger().info("[DRAGON TEST] " + combatResult);

                String playerDistResult = "Distance to player: " + String.format("%.1f", playerDistance) + " blocks";
                player.sendMessage("§7" + playerDistResult);
                plugin.getLogger().info("[DRAGON TEST] " + playerDistResult);

                String combatStatus;
                if (combatDistance > 3 && playerDistance < combatStartPos.distance(player.getLocation())) {
                    combatStatus = "✓ Dragon shows natural combat approach";
                    player.sendMessage("§a" + combatStatus);
                } else {
                    combatStatus = "✗ Dragon combat movement needs improvement";
                    player.sendMessage("§c" + combatStatus);
                }
                plugin.getLogger().info("[DRAGON TEST] " + combatStatus);

                // Summary
                String summary = "📋 MOVEMENT TEST SUMMARY:";
                player.sendMessage("§6" + summary);
                plugin.getLogger().info("[DRAGON TEST] " + summary);

                String idleSummary = "- Idle Movement: " + (idleDistance > 5 ? "PASS" : "FAIL") +
                    " (" + String.format("%.1f", idleDistance) + " blocks)";
                player.sendMessage("§7" + idleSummary);
                plugin.getLogger().info("[DRAGON TEST] " + idleSummary);

                String combatSummary = "- Combat Movement: " + (combatDistance > 3 ? "PASS" : "FAIL") +
                    " (" + String.format("%.1f", combatDistance) + " blocks)";
                player.sendMessage("§7" + combatSummary);
                plugin.getLogger().info("[DRAGON TEST] " + combatSummary);

                String finalResult;
                if (idleDistance > 5 && combatDistance > 3) {
                    finalResult = "🎉 Dragon movement is working naturally!";
                    player.sendMessage("§a" + finalResult);
                } else {
                    finalResult = "⚠ Dragon movement needs attention - check AI implementation";
                    player.sendMessage("§c" + finalResult);
                }
                plugin.getLogger().info("[DRAGON TEST] " + finalResult);
                plugin.getLogger().info("[DRAGON TEST] ==================== TEST COMPLETED ====================");

            }, 100L); // 5 seconds

        }, 200L); // 10 seconds
    }
    
    private void handleCleanup(Player player) {
        String cleanupStart = "Starting cleanup for player: " + player.getName();
        plugin.getLogger().info("[DRAGON TEST] " + cleanupStart);

        int dragonCount = 0;

        for (EnderDragon dragon : player.getWorld().getEntitiesByClass(EnderDragon.class)) {
            String dragonInfo = "Removing dragon: " + dragon.getName() +
                " at " + String.format("%.1f, %.1f, %.1f",
                dragon.getLocation().getX(), dragon.getLocation().getY(), dragon.getLocation().getZ());
            plugin.getLogger().info("[DRAGON TEST] " + dragonInfo);

            dragon.remove();
            dragonCount++;
        }

        // Remove all boss bars
        org.bukkit.Bukkit.getBossBars().forEachRemaining(bossBar -> {
            if (bossBar.getTitle().contains("TEST DRAGON") || bossBar.getTitle().contains("Rồng Ngàn Năm")) {
                plugin.getLogger().info("[DRAGON TEST] Removing boss bar: " + bossBar.getTitle());
                bossBar.removeAll();
            }
        });

        String result = "✓ Đã remove " + dragonCount + " dragons và cleanup boss bars.";
        player.sendMessage("§a" + result);
        plugin.getLogger().info("[DRAGON TEST] " + result);
        plugin.getLogger().info("[DRAGON TEST] Cleanup completed for " + player.getName());
    }
    
    private void handleInfo(Player player) {
        String infoHeader = "=== DRAGON AI INFO - Player: " + player.getName() + " ===";
        player.sendMessage("§6§l" + infoHeader);
        plugin.getLogger().info("[DRAGON TEST] " + infoHeader);

        int dragonCount = player.getWorld().getEntitiesByClass(EnderDragon.class).size();
        String countInfo = "Dragons trong world: " + dragonCount;
        player.sendMessage("§7" + countInfo);
        plugin.getLogger().info("[DRAGON TEST] " + countInfo);

        if (dragonCount > 0) {
            String detailsHeader = "📊 Dragon Details:";
            player.sendMessage("§e" + detailsHeader);
            plugin.getLogger().info("[DRAGON TEST] " + detailsHeader);

            int index = 1;
            for (EnderDragon dragon : player.getWorld().getEntitiesByClass(EnderDragon.class)) {
                double distance = dragon.getLocation().distance(player.getLocation());
                String targetName = dragon.getTarget() != null ? dragon.getTarget().getName() : "None";

                String dragonName = dragon.getName();
                String distanceInfo = "Distance: " + String.format("%.1f", distance) + " blocks";
                String healthInfo = "Health: " + String.format("%.0f", dragon.getHealth()) + "/" +
                    String.format("%.0f", dragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).getValue());
                String targetInfo = "Target: " + targetName;
                String aiInfo = "AI: " + dragon.hasAI();
                String locationInfo = "Location: " + String.format("%.1f, %.1f, %.1f",
                    dragon.getLocation().getX(), dragon.getLocation().getY(), dragon.getLocation().getZ());

                player.sendMessage("§7" + index + ". §f" + dragonName);
                player.sendMessage("   §7" + distanceInfo);
                player.sendMessage("   §7" + healthInfo);
                player.sendMessage("   §7" + targetInfo);
                player.sendMessage("   §7" + aiInfo);

                // Console logging with more details
                plugin.getLogger().info("[DRAGON TEST] Dragon " + index + ": " + dragonName);
                plugin.getLogger().info("[DRAGON TEST]   " + distanceInfo);
                plugin.getLogger().info("[DRAGON TEST]   " + healthInfo);
                plugin.getLogger().info("[DRAGON TEST]   " + targetInfo);
                plugin.getLogger().info("[DRAGON TEST]   " + aiInfo);
                plugin.getLogger().info("[DRAGON TEST]   " + locationInfo);

                index++;
            }
        }

        String tips = "💡 Tips:";
        player.sendMessage("§e" + tips);
        plugin.getLogger().info("[DRAGON TEST] " + tips);

        String[] tipsList = {
            "- Sử dụng /dragontest spawn để tạo test dragon",
            "- Sử dụng /dragontest movement để test natural movement",
            "- Sử dụng /dragontest combat để test tấn công",
            "- Dragon sẽ tự động tìm và tấn công players",
            "- Nếu dragon đứng yên, có thể do AI chưa load"
        };

        for (String tip : tipsList) {
            player.sendMessage("§7" + tip);
            plugin.getLogger().info("[DRAGON TEST] " + tip);
        }

        plugin.getLogger().info("[DRAGON TEST] Info request completed for " + player.getName());
    }
}
