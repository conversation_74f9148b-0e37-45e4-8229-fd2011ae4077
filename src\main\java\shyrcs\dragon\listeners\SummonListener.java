package shyrcs.dragon.listeners;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import shyrcs.dragon.SoulDragonPlugin;

/**
 * Listener for dragon summoning interactions
 */
public class SummonListener implements Listener {
    
    private final SoulDragonPlugin plugin;
    
    public SummonListener(SoulDragonPlugin plugin) {
        this.plugin = plugin;
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        Block clickedBlock = event.getClickedBlock();
        
        if (item == null || clickedBlock == null) {
            return;
        }
        
        // Check if player has permission
        if (!player.hasPermission("souldragon.summon")) {
            return;
        }
        
        // Check if it's an ender eye item
        if (!plugin.getSetupManager().isEnderEye(item)) {
            return;
        }
        
        // Check if clicked block is an End Portal Frame (altar)
        if (clickedBlock.getType() != Material.END_PORTAL_FRAME) {
            return;
        }
        
        Location altarLocation = clickedBlock.getLocation();
        
        // Check if this is a registered altar
        if (!plugin.getDatabase().isAltarAt(altarLocation)) {
            player.sendMessage("§c✗ Đây không phải là tế đàn đã được setup!");
            return;
        }
        
        // Cancel the event to prevent normal interaction
        event.setCancelled(true);
        
        // Check if this altar already has an eye
        if (plugin.getHologramManager().getHologramCount(altarLocation) > 0) {
            player.sendMessage("§c✗ Tế đàn này đã có mắt ender rồi!");
            return;
        }

        // Create hologram if it doesn't exist
        plugin.getHologramManager().createHologram(altarLocation);

        // Add eye to hologram
        if (plugin.getHologramManager().addEyeToHologram(altarLocation)) {
            // Remove item from player
            if (item.getAmount() == 1) {
                player.getInventory().setItemInMainHand(null);
            } else {
                item.setAmount(item.getAmount() - 1);
            }

            // Count total eyes across all altars
            int totalEyes = getTotalEyesPlaced();

            // Send message
            String message = plugin.getConfig().getString("messages.summoning.eye_placed",
                "§e⚡ Đã đặt mắt ender ({current}/8)")
                .replace("{current}", String.valueOf(totalEyes));
            player.sendMessage(message);

            SoulDragonPlugin.info("Ender eye placed at altar by " + player.getName() +
                " (" + totalEyes + "/8)");

            // Check if all 8 altars have eyes
            if (totalEyes >= 8) {
                startDragonSummoning(altarLocation, player);
            }
        } else {
            player.sendMessage("§c✗ Không thể đặt mắt ender tại vị trí này!");
        }
    }
    
    /**
     * Start the dragon summoning sequence
     */
    private void startDragonSummoning(Location altarLocation, Player player) {
        // Remove all 8 ender eyes from all altars
        removeAllEnderEyes();

        // Send summoning started message
        String startMessage = plugin.getConfig().getString("messages.summoning.summoning_started",
            "§c§l⚠ BẮT ĐẦU TRIỆU HỒI RỒNG NGÀN NĂM!");

        // Broadcast to all players in the world
        for (Player p : altarLocation.getWorld().getPlayers()) {
            p.sendMessage(startMessage);
        }
        
        SoulDragonPlugin.info("Dragon summoning started at " + formatLocation(altarLocation) + 
            " by " + player.getName());
        
        // Start the summoning sequence
        plugin.getDragonManager().startSummoning(altarLocation, player);
    }
    
    /**
     * Get total number of eyes placed across all altars
     */
    private int getTotalEyesPlaced() {
        int totalEyes = 0;

        // Get all altar positions and count eyes
        for (var altarData : plugin.getDatabase().getAltarPositions()) {
            Location altarLoc = altarData.toLocation();
            if (altarLoc != null) {
                totalEyes += plugin.getHologramManager().getHologramCount(altarLoc);
            }
        }

        return totalEyes;
    }

    /**
     * Remove all ender eyes from all altars
     */
    private void removeAllEnderEyes() {
        try {
            // Get all altar positions and remove holograms
            for (var altarData : plugin.getDatabase().getAltarPositions()) {
                Location altarLoc = altarData.toLocation();
                if (altarLoc != null) {
                    // Remove hologram from this altar
                    plugin.getHologramManager().removeHologram(altarLoc);
                }
            }

            SoulDragonPlugin.info("All ender eyes removed from altars after summoning started");
        } catch (Exception e) {
            SoulDragonPlugin.error("Failed to remove ender eyes: " + e.getMessage());
        }
    }

    /**
     * Format location for logging
     */
    private String formatLocation(Location loc) {
        return String.format("%s: %.1f, %.1f, %.1f",
            loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }
}
