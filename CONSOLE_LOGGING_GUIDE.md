# Dragon AI Test Console Logging Guide

## Overview
Tất cả dragon test commands giờ đây sẽ log kết quả ra console để dễ copy và analyze.

## Console Log Formats

### 1. Movement Test (`/dragontest movement`)
```
[DRAGON TEST] 🔄 NATURAL MOVEMENT TEST - Player: PlayerName
[DRAGON TEST] 📊 Testing natural dragon flight behavior...
[DRAGON TEST] Phase 1: Testing idle patrol behavior (10 seconds)
[DRAGON TEST] Idle movement distance: 15.3 blocks
[DRAGON TEST] ✓ Dragon shows natural idle movement
[DRAGON TEST] Phase 2: Testing combat approach behavior (5 seconds)
[DRAGON TEST] Combat movement distance: 8.7 blocks
[DRAGON TEST] Distance to player: 12.4 blocks
[DRAGON TEST] ✓ Dragon shows natural combat approach
[DRAGON TEST] 📋 MOVEMENT TEST SUMMARY:
[DRAGON TEST] - Idle Movement: PASS (15.3 blocks)
[DRAGON TEST] - Combat Movement: PASS (8.7 blocks)
[DRAGON TEST] 🎉 Dragon movement is working naturally!
[DRAGON TEST] ==================== TEST COMPLETED ====================
```

### 2. Spawn Test (`/dragontest spawn`)
```
[DRAGON TEST] ✓ Spawning test dragon for player: PlayerName at 100.0, 80.0, 200.0
[DRAGON TEST] Dragon configured: Health=1000, AI=true, Name=TEST DRAGON
[DRAGON TEST] Boss bar created and player added
[DRAGON TEST] ✓ Test dragon spawned với AI! Dragon sẽ tấn công bạn.
[DRAGON TEST] Custom AI started, target set to: PlayerName
```

### 3. Full Scenario Test (`/dragontest scenario`)
```
[DRAGON AI TEST] === DRAGON AI TEST RESULTS - Player: PlayerName ===
[DRAGON AI TEST] ✓ Dragon Spawn: Dragon spawned successfully
[DRAGON AI TEST] ✓ AI Initialization: AI started successfully
[DRAGON AI TEST] ✓ Movement Test: Dragon moved 15.3 blocks
[DRAGON AI TEST] ✓ Targeting Test: Dragon correctly targets test player
[DRAGON AI TEST] ✓ Combat Test: Player took damage: 4.5 hearts
[DRAGON AI TEST] ✓ Boss Bar Test: Boss bar working correctly
[DRAGON AI TEST] 📊 Summary: 6/6 tests passed
[DRAGON AI TEST] 🎉 ALL TESTS PASSED! Dragon AI is working correctly!
[DRAGON AI TEST] ==================== DETAILED RESULTS ====================
[DRAGON AI TEST] Test Environment: world
[DRAGON AI TEST] Dragon Health: 500.0
[DRAGON AI TEST] Boss Bar Active: true
[DRAGON AI TEST] Custom AI Active: true
[DRAGON AI TEST] Dragon Spawn - PASS - Dragon spawned successfully
[DRAGON AI TEST] AI Initialization - PASS - AI started successfully
[DRAGON AI TEST] Movement Test - PASS - Dragon moved 15.3 blocks
[DRAGON AI TEST] Targeting Test - PASS - Dragon correctly targets test player
[DRAGON AI TEST] Combat Test - PASS - Player took damage: 4.5 hearts
[DRAGON AI TEST] Boss Bar Test - PASS - Boss bar working correctly
[DRAGON AI TEST] Overall Score: 6/6 (100.0%)
[DRAGON AI TEST] ==================== TEST COMPLETED ====================
[DRAGON AI TEST] Test cleanup completed.
```

### 4. Info Command (`/dragontest info`)
```
[DRAGON TEST] === DRAGON AI INFO - Player: PlayerName ===
[DRAGON TEST] Dragons trong world: 1
[DRAGON TEST] 📊 Dragon Details:
[DRAGON TEST] Dragon 1: TEST DRAGON
[DRAGON TEST]   Distance: 25.3 blocks
[DRAGON TEST]   Health: 1000/1000
[DRAGON TEST]   Target: PlayerName
[DRAGON TEST]   AI: true
[DRAGON TEST]   Location: 100.0, 80.0, 200.0
[DRAGON TEST] 💡 Tips:
[DRAGON TEST] - Sử dụng /dragontest spawn để tạo test dragon
[DRAGON TEST] - Sử dụng /dragontest movement để test natural movement
[DRAGON TEST] - Sử dụng /dragontest combat để test tấn công
[DRAGON TEST] - Dragon sẽ tự động tìm và tấn công players
[DRAGON TEST] - Nếu dragon đứng yên, có thể do AI chưa load
[DRAGON TEST] Info request completed for PlayerName
```

### 5. Cleanup Command (`/dragontest cleanup`)
```
[DRAGON TEST] Starting cleanup for player: PlayerName
[DRAGON TEST] Removing dragon: TEST DRAGON at 100.0, 80.0, 200.0
[DRAGON TEST] Removing boss bar: §c§lTEST DRAGON
[DRAGON TEST] ✓ Đã remove 1 dragons và cleanup boss bars.
[DRAGON TEST] Cleanup completed for PlayerName
```

## How to Use Console Logs

### 1. Copy Test Results
- Chạy test commands
- Mở server console hoặc log file
- Search cho `[DRAGON TEST]` hoặc `[DRAGON AI TEST]`
- Copy toàn bộ test results

### 2. Analyze Results
- **PASS/FAIL status**: Dễ dàng thấy test nào pass/fail
- **Numerical data**: Distance, health, damage values
- **Timestamps**: Khi nào test được chạy
- **Player info**: Ai chạy test

### 3. Debug Issues
- **Movement problems**: Check idle/combat movement distances
- **AI problems**: Check targeting, health, AI status
- **Performance**: Check test completion times

### 4. Share Results
- Copy console logs để share với team
- Include trong bug reports
- Document test results

## Quick Commands for Testing

```bash
# Test natural movement
/dragontest spawn
/dragontest movement

# Full automated test
/dragontest scenario

# Monitor dragon status
/dragontest info

# Clean up
/dragontest cleanup
```

## Expected Good Results

### Movement Test - PASS
```
- Idle Movement: PASS (>5.0 blocks)
- Combat Movement: PASS (>3.0 blocks)
🎉 Dragon movement is working naturally!
```

### Full Scenario - PASS
```
Overall Score: 6/6 (100.0%)
🎉 ALL TESTS PASSED! Dragon AI is working correctly!
```

## Expected Bad Results

### Movement Test - FAIL
```
- Idle Movement: FAIL (0.2 blocks)
- Combat Movement: FAIL (0.1 blocks)
⚠ Dragon movement needs attention - check AI implementation
```

### Full Scenario - FAIL
```
Overall Score: 3/6 (50.0%)
⚠ Some tests failed. Dragon AI needs attention.
```

## Tips
- Always check console logs after running tests
- Copy logs before running cleanup
- Use logs to track improvements over time
- Share logs when reporting issues
