package shyrcs.dragon.managers;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.database.DragonDatabase;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Manager for dragon summoning and battle mechanics
 */
public class DragonManager {
    
    private final SoulDragonPlugin plugin;
    private final Map<Location, BukkitTask> summoningTasks;
    private final Map<Location, EnderDragon> activeDragons;
    
    public DragonManager(SoulDragonPlugin plugin) {
        this.plugin = plugin;
        this.summoningTasks = new HashMap<>();
        this.activeDragons = new HashMap<>();
    }
    
    /**
     * Start dragon summoning sequence
     */
    public void startSummoning(Location altarLocation, Player summoner) {
        // Check if summoning is already in progress
        if (summoningTasks.containsKey(altarLocation)) {
            return;
        }
        
        // Get boss spawn location
        Location bossLocation = getBossSpawnLocation(altarLocation);
        if (bossLocation == null) {
            summoner.sendMessage("§c✗ Không tìm thấy vị trí spawn boss!");
            return;
        }
        
        // Start the summoning sequence
        BukkitTask task = new SummoningSequence(altarLocation, bossLocation, summoner).runTaskTimer(plugin, 0L, 20L);
        summoningTasks.put(altarLocation, task);
    }
    
    /**
     * Get boss spawn location (60 blocks above the nearest boss position)
     */
    private Location getBossSpawnLocation(Location altarLocation) {
        List<DragonDatabase.LocationData> bossPositions = plugin.getDatabase().getBossPositions();

        if (bossPositions.isEmpty()) {
            return null;
        }

        // Find the nearest boss position
        DragonDatabase.LocationData nearest = null;
        double minDistance = Double.MAX_VALUE;

        for (DragonDatabase.LocationData pos : bossPositions) {
            Location bossPos = pos.toLocation();
            if (bossPos != null && bossPos.getWorld().equals(altarLocation.getWorld())) {
                double distance = altarLocation.distance(bossPos);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = pos;
                }
            }
        }

        if (nearest != null) {
            Location baseLocation = nearest.toLocation();
            return baseLocation.clone().add(0, 60, 0); // 60 blocks above
        }

        return null;
    }
    
    /**
     * Spawn End Crystal at location
     */
    private void spawnEndCrystal(Location location) {
        try {
            location.getWorld().spawnEntity(location, EntityType.END_CRYSTAL);
            
            // Add particle effects
            location.getWorld().spawnParticle(Particle.EXPLOSION, location, 1);
            location.getWorld().playSound(location, Sound.BLOCK_GLASS_PLACE, 1.0f, 1.0f);
            
        } catch (Exception e) {
            plugin.getLogger().warning("Failed to spawn End Crystal: " + e.getMessage());
        }
    }
    
    /**
     * Spawn the dragon at location
     */
    private void spawnDragon(Location location, Location altarLocation) {
        try {
            // Create explosion effect
            double explosionPower = plugin.getConfig().getDouble("summoning.explosion.power", 4.0);
            boolean breakBlocks = plugin.getConfig().getBoolean("summoning.explosion.break_blocks", false);
            boolean causeFire = plugin.getConfig().getBoolean("summoning.explosion.cause_fire", false);
            
            location.getWorld().createExplosion(location, (float) explosionPower, causeFire, breakBlocks);
            
            // Spawn dragon
            EnderDragon dragon = (EnderDragon) location.getWorld().spawnEntity(location, EntityType.ENDER_DRAGON);

            // Configure dragon immediately
            String dragonName = plugin.getConfig().getString("dragon.name", "§f§lRồng Ngàn Năm");
            double dragonHealth = plugin.getConfig().getDouble("dragon.health", 1000.0); // Reduced to 1000

            // Ensure health is within valid range (max 1024)
            if (dragonHealth > 1024.0) {
                dragonHealth = 1024.0;
                SoulDragonPlugin.warn("Dragon health reduced to 1024 (max allowed value)");
            }

            dragon.customName(net.kyori.adventure.text.Component.text(dragonName));
            dragon.setCustomNameVisible(true);
            dragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).setBaseValue(dragonHealth);
            dragon.setHealth(dragonHealth);

            // Make dragon aggressive immediately
            dragon.setAI(true);

            // Find and target nearest player immediately
            final Player[] nearestPlayerRef = {null};
            double nearestDistance = Double.MAX_VALUE;
            for (Player p : location.getWorld().getPlayers()) {
                if (p.getGameMode() != org.bukkit.GameMode.CREATIVE &&
                    p.getGameMode() != org.bukkit.GameMode.SPECTATOR) {
                    double distance = p.getLocation().distance(location);
                    if (distance < nearestDistance) {
                        nearestDistance = distance;
                        nearestPlayerRef[0] = p;
                    }
                }
            }

            if (nearestPlayerRef[0] != null) {
                dragon.setTarget(nearestPlayerRef[0]);
            }

            // Create boss bar
            org.bukkit.boss.BossBar bossBar = org.bukkit.Bukkit.createBossBar(
                dragonName,
                org.bukkit.boss.BarColor.WHITE,
                org.bukkit.boss.BarStyle.SOLID
            );
            bossBar.setProgress(1.0);

            // Add all players in the world to the boss bar
            for (Player p : location.getWorld().getPlayers()) {
                bossBar.addPlayer(p);
            }

            // Create ULTIMATE DRAGON AI FSM with delay to ensure dragon is fully loaded
            org.bukkit.Bukkit.getScheduler().runTaskLater(plugin, () -> {
                // Use Dragon AI FSM system (ultimate primary system)
                shyrcs.dragon.dragon.DragonAIFSM dragonAIFSM =
                    new shyrcs.dragon.dragon.DragonAIFSM(plugin, dragon);
                dragonAIFSM.startAI();

                // Also start Custom AI for boss bar and combat features only
                shyrcs.dragon.dragon.CustomDragon customDragon =
                    new shyrcs.dragon.dragon.CustomDragon(plugin, dragon, bossBar);
                customDragon.startAI();

                // Enable vanilla AI for Dragon AI FSM to work with
                dragon.setAI(true);
                // Let Dragon AI FSM handle targeting naturally

                plugin.getLogger().info("[DRAGON MANAGER] ULTIMATE AI SYSTEM: Dragon AI FSM + Custom AI (boss bar only) started for dragon");
            }, 10L); // 10 tick delay for better initialization

            // Store active dragon
            activeDragons.put(altarLocation, dragon);

            // Remove all ender eyes from altars after successful summoning
            removeAllEnderEyes();

            // Send spawn message
            String spawnMessage = plugin.getConfig().getString("messages.summoning.dragon_spawned",
                "§4§l🐲 RỒNG NGÀN NĂM ĐÃ XUẤT HIỆN!");

            for (Player p : location.getWorld().getPlayers()) {
                p.sendMessage(spawnMessage);
            }

            SoulDragonPlugin.info("Dragon spawned at " + formatLocation(location));
            
        } catch (Exception e) {
            plugin.getLogger().severe("Failed to spawn dragon: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Cleanup all dragons and related entities
     */
    public void cleanup() {
        // Cancel all summoning tasks
        for (BukkitTask task : summoningTasks.values()) {
            if (task != null && !task.isCancelled()) {
                task.cancel();
            }
        }
        summoningTasks.clear();
        
        // Remove all active dragons
        for (EnderDragon dragon : activeDragons.values()) {
            if (dragon != null && !dragon.isDead()) {
                dragon.remove();
            }
        }
        activeDragons.clear();
    }
    
    /**
     * Remove all ender eyes from altars after successful summoning
     */
    private void removeAllEnderEyes() {
        try {
            // Get all altar positions
            for (var altarData : plugin.getDatabase().getAltarPositions()) {
                Location altarLoc = altarData.toLocation();
                if (altarLoc != null) {
                    // Remove hologram from this altar
                    plugin.getHologramManager().removeHologram(altarLoc);
                }
            }

            SoulDragonPlugin.info("All ender eyes removed from altars after dragon summoning");
        } catch (Exception e) {
            SoulDragonPlugin.error("Failed to remove ender eyes: " + e.getMessage());
        }
    }

    /**
     * Format location for logging
     */
    private String formatLocation(Location loc) {
        return String.format("%s: %.1f, %.1f, %.1f",
            loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }

    /**
     * Summoning sequence task
     */
    private class SummoningSequence extends BukkitRunnable {

        private final Location altarLocation;
        private final Location bossLocation;
        private final Player summoner;
        private int secondsElapsed;
        private int crystalsSpawned;
        private int poemLineIndex;

        public SummoningSequence(Location altarLocation, Location bossLocation, Player summoner) {
            this.altarLocation = altarLocation;
            this.bossLocation = bossLocation;
            this.summoner = summoner;
            this.secondsElapsed = 0;
            this.crystalsSpawned = 0;
            this.poemLineIndex = 0;
        }

        @Override
        public void run() {
            secondsElapsed++;

            // Spawn End Crystals (1 per second for 12 seconds)
            if (secondsElapsed <= 12) {
                spawnCrystalForSequence();
            }

            // Display poem lines
            displayPoemLine();

            // Spawn dragon after 12 seconds
            if (secondsElapsed >= 12) {
                spawnDragon(bossLocation, altarLocation);

                // Remove holograms from altar
                plugin.getHologramManager().removeAllHologramsAt(altarLocation);

                // Remove this task from tracking
                summoningTasks.remove(altarLocation);

                // Cancel this task
                this.cancel();
            }
        }

        /**
         * Spawn crystal for the summoning sequence
         */
        private void spawnCrystalForSequence() {
            List<DragonDatabase.LocationData> crystalPositions = plugin.getDatabase().getCrystalPositions();

            if (crystalsSpawned < crystalPositions.size()) {
                DragonDatabase.LocationData crystalData = crystalPositions.get(crystalsSpawned);
                Location crystalLocation = crystalData.toLocation();

                if (crystalLocation != null && crystalLocation.getWorld().equals(altarLocation.getWorld())) {
                    spawnEndCrystal(crystalLocation);
                    crystalsSpawned++;
                }
            }
        }

        /**
         * Display poem lines during summoning
         */
        private void displayPoemLine() {
            List<String> poemLines = plugin.getConfig().getStringList("summoning.poem");

            if (poemLineIndex < poemLines.size()) {
                // Display poem lines at specific intervals
                if (shouldDisplayPoemLine()) {
                    String line = poemLines.get(poemLineIndex);

                    // Broadcast to all players in the world
                    for (Player p : altarLocation.getWorld().getPlayers()) {
                        p.sendMessage(line);
                    }

                    poemLineIndex++;
                }
            }
        }

        /**
         * Determine if we should display a poem line at this second
         */
        private boolean shouldDisplayPoemLine() {
            // Display poem lines at seconds: 1, 2, 4, 5, 7, 8, 10, 11
            return switch (secondsElapsed) {
                case 1, 2, 4, 5, 7, 8, 10, 11 -> true;
                default -> false;
            };
        }
    }
}
