package shyrcs.dragon.test;

import org.bukkit.Location;
import org.bukkit.entity.EnderDragon;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;
import shyrcs.dragon.dragon.DragonAIFSM;

import java.util.ArrayList;
import java.util.List;

/**
 * Automated test scenario for Dragon AI
 */
public class DragonAITestScenario {
    
    private final SoulDragonPlugin plugin;
    private final Player testPlayer;
    private final List<TestResult> results;
    private EnderDragon testDragon;
    private CustomDragon customDragon;
    private DragonAIFSM dragonAIFSM;
    private org.bukkit.boss.BossBar testBossBar;
    
    public DragonAITestScenario(SoulDragonPlugin plugin, Player testPlayer) {
        this.plugin = plugin;
        this.testPlayer = testPlayer;
        this.results = new ArrayList<>();
    }
    
    /**
     * Start the automated test scenario
     */
    public void startTest() {
        testPlayer.sendMessage("§6§l=== DRAGON AI TEST SCENARIO ===");
        testPlayer.sendMessage("§e🧪 Bắt đầu test tự động...");
        
        // Phase 1: Spawn Test
        runPhase1_SpawnTest();
    }
    
    private void runPhase1_SpawnTest() {
        testPlayer.sendMessage("§a📋 Phase 1: Dragon Spawn Test");
        
        Location spawnLoc = testPlayer.getLocation().add(0, 30, 0);
        
        try {
            // Spawn dragon
            testDragon = (EnderDragon) spawnLoc.getWorld().spawnEntity(spawnLoc, EntityType.ENDER_DRAGON);
            testDragon.customName(net.kyori.adventure.text.Component.text("§c§lTEST AI DRAGON"));
            testDragon.setCustomNameVisible(true);
            testDragon.getAttribute(org.bukkit.attribute.Attribute.MAX_HEALTH).setBaseValue(500.0);
            testDragon.setHealth(500.0);
            
            // Create boss bar
            testBossBar = org.bukkit.Bukkit.createBossBar(
                "§c§lTEST AI DRAGON",
                org.bukkit.boss.BarColor.RED,
                org.bukkit.boss.BarStyle.SOLID
            );
            testBossBar.addPlayer(testPlayer);
            
            results.add(new TestResult("Dragon Spawn", true, "Dragon spawned successfully"));
            testPlayer.sendMessage("§a✓ Dragon spawned successfully");
            
            // Wait then start AI
            new BukkitRunnable() {
                @Override
                public void run() {
                    runPhase2_AIInitialization();
                }
            }.runTaskLater(plugin, 20L); // 1 second delay
            
        } catch (Exception e) {
            results.add(new TestResult("Dragon Spawn", false, "Failed: " + e.getMessage()));
            testPlayer.sendMessage("§c✗ Dragon spawn failed: " + e.getMessage());
            endTest();
        }
    }
    
    private void runPhase2_AIInitialization() {
        testPlayer.sendMessage("§a📋 Phase 2: AI Initialization Test");
        
        try {
            // Initialize ULTIMATE DRAGON AI FSM (single primary system)
            dragonAIFSM = new DragonAIFSM(plugin, testDragon);
            dragonAIFSM.startAI();

            // Initialize Custom AI ONLY for boss bar and combat features (no movement)
            customDragon = new CustomDragon(plugin, testDragon, testBossBar);
            customDragon.startAI();

            // Force AI settings
            testDragon.setAI(true); // Enable vanilla AI for Dragon AI FSM
            // Don't force target - let Dragon AI FSM handle it naturally

            results.add(new TestResult("AI Initialization", true, "Dragon AI FSM (ultimate system) + Custom AI (boss bar only) started successfully"));
            testPlayer.sendMessage("§a✓ Dragon AI FSM (ultimate system) initialized successfully");
            plugin.getLogger().info("[DRAGON AI TEST] ULTIMATE AI SYSTEM: Dragon AI FSM + Custom AI (boss bar only) started for test scenario");

            // Wait then test movement
            new BukkitRunnable() {
                @Override
                public void run() {
                    runPhase3_MovementTest();
                }
            }.runTaskLater(plugin, 40L); // 2 seconds delay

        } catch (Exception e) {
            results.add(new TestResult("AI Initialization", false, "Failed: " + e.getMessage()));
            testPlayer.sendMessage("§c✗ AI initialization failed: " + e.getMessage());
            plugin.getLogger().severe("[DRAGON AI TEST] AI initialization failed: " + e.getMessage());
            endTest();
        }
    }
    
    private void runPhase3_MovementTest() {
        testPlayer.sendMessage("§a📋 Phase 3: Movement Test");
        
        Location initialPos = testDragon.getLocation().clone();
        
        // Wait 5 seconds and check if dragon moved
        new BukkitRunnable() {
            @Override
            public void run() {
                Location currentPos = testDragon.getLocation();
                double distance = initialPos.distance(currentPos);
                
                if (distance > 2.0) {
                    results.add(new TestResult("Movement Test", true, 
                        "Dragon moved " + String.format("%.1f", distance) + " blocks"));
                    testPlayer.sendMessage("§a✓ Dragon is moving! Distance: " + String.format("%.1f", distance) + " blocks");
                } else {
                    results.add(new TestResult("Movement Test", false, 
                        "Dragon barely moved: " + String.format("%.1f", distance) + " blocks"));
                    testPlayer.sendMessage("§c✗ Dragon is not moving enough! Distance: " + String.format("%.1f", distance) + " blocks");
                }
                
                runPhase4_TargetingTest();
            }
        }.runTaskLater(plugin, 100L); // 5 seconds
    }
    
    private void runPhase4_TargetingTest() {
        testPlayer.sendMessage("§a📋 Phase 4: Targeting Test");
        
        boolean hasTarget = testDragon.getTarget() != null;
        boolean targetIsPlayer = testDragon.getTarget() == testPlayer;
        
        if (hasTarget && targetIsPlayer) {
            results.add(new TestResult("Targeting Test", true, "Dragon correctly targets test player"));
            testPlayer.sendMessage("§a✓ Dragon is targeting you correctly");
        } else if (hasTarget) {
            results.add(new TestResult("Targeting Test", false, 
                "Dragon has wrong target: " + testDragon.getTarget().getName()));
            testPlayer.sendMessage("§c✗ Dragon targeting wrong entity: " + testDragon.getTarget().getName());
        } else {
            results.add(new TestResult("Targeting Test", false, "Dragon has no target"));
            testPlayer.sendMessage("§c✗ Dragon has no target");
        }
        
        runPhase5_CombatTest();
    }
    
    private void runPhase5_CombatTest() {
        testPlayer.sendMessage("§a📋 Phase 5: Combat Test");
        testPlayer.sendMessage("§e⚠ Dragon sẽ test combat trong 3 giây...");
        
        // Move dragon close for combat test
        Location combatPos = testPlayer.getLocation().add(0, 8, 0);
        testDragon.teleport(combatPos);
        
        double initialHealth = testPlayer.getHealth();
        
        new BukkitRunnable() {
            @Override
            public void run() {
                testPlayer.sendMessage("§c⚔ Combat test starting!");
                
                // Wait 3 seconds and check if player took damage
                new BukkitRunnable() {
                    @Override
                    public void run() {
                        double currentHealth = testPlayer.getHealth();
                        boolean tookDamage = currentHealth < initialHealth;
                        
                        if (tookDamage) {
                            results.add(new TestResult("Combat Test", true, 
                                "Player took damage: " + (initialHealth - currentHealth) + " hearts"));
                            testPlayer.sendMessage("§a✓ Dragon combat working! Damage taken: " + 
                                String.format("%.1f", initialHealth - currentHealth) + " hearts");
                        } else {
                            results.add(new TestResult("Combat Test", false, "Player took no damage"));
                            testPlayer.sendMessage("§c✗ Dragon combat not working - no damage dealt");
                        }
                        
                        runPhase6_BossBarTest();
                    }
                }.runTaskLater(plugin, 60L); // 3 seconds
            }
        }.runTaskLater(plugin, 60L); // 3 seconds delay
    }
    
    private void runPhase6_BossBarTest() {
        testPlayer.sendMessage("§a📋 Phase 6: Boss Bar Test");
        
        boolean bossBarExists = testBossBar != null;
        boolean playerInBossBar = bossBarExists && testBossBar.getPlayers().contains(testPlayer);
        
        if (bossBarExists && playerInBossBar) {
            results.add(new TestResult("Boss Bar Test", true, "Boss bar working correctly"));
            testPlayer.sendMessage("§a✓ Boss bar is working correctly");
        } else {
            results.add(new TestResult("Boss Bar Test", false, 
                "Boss bar issues - exists: " + bossBarExists + ", player added: " + playerInBossBar));
            testPlayer.sendMessage("§c✗ Boss bar has issues");
        }
        
        // End test after 2 seconds
        new BukkitRunnable() {
            @Override
            public void run() {
                endTest();
            }
        }.runTaskLater(plugin, 40L);
    }
    
    private void endTest() {
        String resultsHeader = "=== DRAGON AI TEST RESULTS - Player: " + testPlayer.getName() + " ===";
        testPlayer.sendMessage("§6§l" + resultsHeader);
        plugin.getLogger().info("[DRAGON AI TEST] " + resultsHeader);

        int passed = 0;
        int total = results.size();

        for (TestResult result : results) {
            String status = result.passed ? "✓" : "✗";
            String resultLine = status + " " + result.testName + ": " + result.details;

            testPlayer.sendMessage((result.passed ? "§a" : "§c") + status + " §f" + result.testName + ": §7" + result.details);
            plugin.getLogger().info("[DRAGON AI TEST] " + resultLine);

            if (result.passed) passed++;
        }

        String summary = "📊 Summary: " + passed + "/" + total + " tests passed";
        testPlayer.sendMessage("§e" + summary);
        plugin.getLogger().info("[DRAGON AI TEST] " + summary);

        String finalResult;
        if (passed == total) {
            finalResult = "🎉 ALL TESTS PASSED! Dragon AI is working correctly!";
            testPlayer.sendMessage("§a" + finalResult);
        } else {
            finalResult = "⚠ Some tests failed. Dragon AI needs attention.";
            testPlayer.sendMessage("§c" + finalResult);
        }
        plugin.getLogger().info("[DRAGON AI TEST] " + finalResult);

        // Detailed breakdown for console
        plugin.getLogger().info("[DRAGON AI TEST] ==================== DETAILED RESULTS ====================");
        plugin.getLogger().info("[DRAGON AI TEST] Test Environment: " + testPlayer.getWorld().getName());
        plugin.getLogger().info("[DRAGON AI TEST] Dragon Health: " + (testDragon != null ? testDragon.getHealth() : "N/A"));
        plugin.getLogger().info("[DRAGON AI TEST] Boss Bar Active: " + (testBossBar != null));
        plugin.getLogger().info("[DRAGON AI TEST] Custom AI Active: " + (customDragon != null));
        plugin.getLogger().info("[DRAGON AI TEST] AI System: ULTIMATE DRAGON AI FSM");

        // Dragon AI FSM specific info (primary and only movement system)
        if (dragonAIFSM != null) {
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM Primary State: " + dragonAIFSM.getPrimaryState());
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM Secondary State: " + dragonAIFSM.getSecondaryState());
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM Emotional State: " + dragonAIFSM.getEmotionalState());
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM Cognitive State: " + dragonAIFSM.getCognitiveState());
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM Aggression Level: " +
                String.format("%.2f", dragonAIFSM.getAggressionLevel()));
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM Circling Radius: " +
                String.format("%.1f", dragonAIFSM.getCirclingRadius()));
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM Precision Control: " + dragonAIFSM.isPrecisionControlActive());
            plugin.getLogger().info("[DRAGON AI TEST] Dragon Phase: " + testDragon.getPhase());
        } else {
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM: NOT ACTIVE");
        }

        for (TestResult result : results) {
            plugin.getLogger().info("[DRAGON AI TEST] " + result.testName + " - " +
                (result.passed ? "PASS" : "FAIL") + " - " + result.details);
        }

        plugin.getLogger().info("[DRAGON AI TEST] Overall Score: " + passed + "/" + total +
            " (" + String.format("%.1f", (passed * 100.0 / total)) + "%)");
        plugin.getLogger().info("[DRAGON AI TEST] ==================== TEST COMPLETED ====================");

        // Cleanup - SINGLE AI SYSTEM
        if (testDragon != null && !testDragon.isDead()) {
            testDragon.remove();
        }
        if (testBossBar != null) {
            testBossBar.removeAll();
        }
        if (customDragon != null) {
            customDragon.cleanup();
        }
        if (dragonAIFSM != null) {
            dragonAIFSM.stopAI();
            plugin.getLogger().info("[DRAGON AI TEST] Dragon AI FSM stopped");
        }

        String cleanup = "Test cleanup completed.";
        testPlayer.sendMessage("§7" + cleanup);
        plugin.getLogger().info("[DRAGON AI TEST] " + cleanup);
    }
    
    private static class TestResult {
        final String testName;
        final boolean passed;
        final String details;
        
        TestResult(String testName, boolean passed, String details) {
            this.testName = testName;
            this.passed = passed;
            this.details = details;
        }
    }
}
