package shyrcs.dragon.dragon;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import shyrcs.dragon.SoulDragonPlugin;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * MATHEMATICAL DRAGON AI - Based on Minecraft Generation Research
 * 
 * Pure mathematical implementation of Ender Dragon AI using:
 * - Graph-based pathfinding with weighted nodes
 * - Polar coordinate movement patterns
 * - Behavioral state machine with probability transitions
 * - Linear Congruential Generator for deterministic randomness
 * - Authentic Minecraft algorithms
 * 
 * Research: https://github.com/IsolatedSingularity/Minecraft-Generation
 */
public class DragonAIMathematical extends BukkitRunnable {
    
    private final SoulDragonPlugin plugin;
    private final org.bukkit.entity.EnderDragon bukkitDragon;
    private final Location homeLocation;
    private final MathematicalRandom mathRandom;
    
    // Mathematical State Machine - Based on Research
    private DragonState currentState;
    private int stateTicks;
    private int totalTicks;
    
    // Pathfinding Graph - Exact Implementation from Research
    private final Map<String, Vec3> pathfindingNodes;
    private final Map<String, List<String>> nodeConnections;
    private String currentTargetNode;
    
    // Mathematical Parameters
    private double aggressionLevel;
    private double circlingAngle;
    private double pillarRadius = 76.0; // From research
    private double outerRadius = 100.0;
    private double innerRadius = 60.0;
    private double centerRadius = 30.0;
    
    // Behavioral Probabilities - From Research
    private static final double PERCH_BASE_PROBABILITY = 1.0 / 3.0;
    private static final double STATE_TRANSITION_PROBABILITY = 0.4;
    
    public enum DragonState {
        HOLDING,     // Circle around outer nodes
        STRAFING,    // Move in straight lines with fireball patterns
        APPROACH,    // Move toward fountain gradually
        LANDING,     // Spiral down toward perch
        PERCHING,    // Stay near fountain with minimal movement
        TAKEOFF,     // Move away from fountain in expanding spiral
        CHARGING     // Fast direct movement toward player
    }
    
    public DragonAIMathematical(SoulDragonPlugin plugin, org.bukkit.entity.EnderDragon bukkitDragon) {
        this.plugin = plugin;
        this.bukkitDragon = bukkitDragon;
        this.homeLocation = bukkitDragon.getLocation().clone();
        this.mathRandom = new MathematicalRandom(System.currentTimeMillis());
        
        // Initialize state
        this.currentState = DragonState.HOLDING;
        this.stateTicks = 0;
        this.totalTicks = 0;
        this.aggressionLevel = 0.3 + mathRandom.nextDouble() * 0.4;
        this.circlingAngle = 0.0;
        
        // Initialize pathfinding graph
        this.pathfindingNodes = new HashMap<>();
        this.nodeConnections = new HashMap<>();
        initializePathfindingGraph();
        
        plugin.getLogger().info("[MATHEMATICAL AI] Dragon AI initialized with mathematical precision");
    }
    
    private void initializePathfindingGraph() {
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());
        
        // Central fountain node
        pathfindingNodes.put("fountain", homeVec);
        
        // End pillars (obsidian towers) - 10 pillars in a ring
        for (int i = 0; i < 10; i++) {
            double angle = (2 * Math.PI * i) / 10.0;
            Vec3 pillarPos = homeVec.add(
                pillarRadius * Math.cos(angle),
                0,
                pillarRadius * Math.sin(angle)
            );
            pathfindingNodes.put("pillar_" + i, pillarPos);
        }
        
        // Outer ring nodes - 12 nodes for wide circling
        for (int i = 0; i < 12; i++) {
            double angle = (2 * Math.PI * i) / 12.0;
            Vec3 outerPos = homeVec.add(
                outerRadius * Math.cos(angle),
                10,
                outerRadius * Math.sin(angle)
            );
            pathfindingNodes.put("outer_" + i, outerPos);
        }
        
        // Inner ring nodes - 8 nodes for medium circling
        for (int i = 0; i < 8; i++) {
            double angle = (2 * Math.PI * i) / 8.0;
            Vec3 innerPos = homeVec.add(
                innerRadius * Math.cos(angle),
                8,
                innerRadius * Math.sin(angle)
            );
            pathfindingNodes.put("inner_" + i, innerPos);
        }
        
        // Center ring nodes - 4 nodes for close maneuvering
        for (int i = 0; i < 4; i++) {
            double angle = (2 * Math.PI * i) / 4.0;
            Vec3 centerPos = homeVec.add(
                centerRadius * Math.cos(angle),
                5,
                centerRadius * Math.sin(angle)
            );
            pathfindingNodes.put("center_" + i, centerPos);
        }
        
        // Initialize connections (simplified for now)
        initializeNodeConnections();
        
        // Set initial target
        this.currentTargetNode = "outer_0";
    }
    
    private void initializeNodeConnections() {
        // Connect outer ring nodes in sequence
        for (int i = 0; i < 12; i++) {
            String currentNode = "outer_" + i;
            String nextNode = "outer_" + ((i + 1) % 12);
            nodeConnections.computeIfAbsent(currentNode, k -> new ArrayList<>()).add(nextNode);
        }
        
        // Connect inner ring nodes in sequence
        for (int i = 0; i < 8; i++) {
            String currentNode = "inner_" + i;
            String nextNode = "inner_" + ((i + 1) % 8);
            nodeConnections.computeIfAbsent(currentNode, k -> new ArrayList<>()).add(nextNode);
        }
        
        // Connect center ring nodes in sequence
        for (int i = 0; i < 4; i++) {
            String currentNode = "center_" + i;
            String nextNode = "center_" + ((i + 1) % 4);
            nodeConnections.computeIfAbsent(currentNode, k -> new ArrayList<>()).add(nextNode);
        }
    }
    
    @Override
    public void run() {
        if (bukkitDragon == null || bukkitDragon.isDead()) {
            cancel();
            return;
        }
        
        totalTicks++;
        stateTicks++;
        
        // Mathematical state transitions based on probability
        if (stateTicks > 60) { // Minimum 3 seconds per state
            evaluateStateTransition();
        }
        
        // Execute current state behavior
        executeCurrentState();
        
        // Log state every 10 seconds
        if (totalTicks % 200 == 0) {
            logMathematicalState();
        }
    }
    
    private void evaluateStateTransition() {
        double transitionProbability = calculateTransitionProbability();
        
        if (mathRandom.nextDouble() < transitionProbability) {
            DragonState newState = selectNextState();
            if (newState != currentState) {
                transitionToState(newState);
            }
        }
    }
    
    private double calculateTransitionProbability() {
        // Base probability modified by aggression and player proximity
        double baseProbability = STATE_TRANSITION_PROBABILITY;
        
        Player nearestPlayer = findNearestPlayer();
        if (nearestPlayer != null) {
            double distance = bukkitDragon.getLocation().distance(nearestPlayer.getLocation());
            double proximityFactor = Math.max(0.1, 1.0 - (distance / 100.0));
            baseProbability *= (1.0 + aggressionLevel * proximityFactor);
        }
        
        return Math.min(0.8, baseProbability);
    }
    
    private DragonState selectNextState() {
        // Probability-weighted state selection
        Player nearestPlayer = findNearestPlayer();
        
        if (nearestPlayer == null) {
            return DragonState.HOLDING;
        }
        
        double distance = bukkitDragon.getLocation().distance(nearestPlayer.getLocation());
        double rand = mathRandom.nextDouble();
        
        // Distance-based state selection with mathematical precision
        if (distance < 20) {
            if (rand < 0.4) return DragonState.CHARGING;
            if (rand < 0.7) return DragonState.STRAFING;
            return DragonState.APPROACH;
        } else if (distance < 50) {
            if (rand < 0.3) return DragonState.STRAFING;
            if (rand < 0.6) return DragonState.APPROACH;
            return DragonState.HOLDING;
        } else {
            if (rand < 0.8) return DragonState.HOLDING;
            return DragonState.STRAFING;
        }
    }
    
    private void transitionToState(DragonState newState) {
        plugin.getLogger().info("[MATHEMATICAL AI] State transition: " + currentState + " -> " + newState);
        this.currentState = newState;
        this.stateTicks = 0;
        
        // Update target node based on new state
        updateTargetNode();
    }
    
    private void updateTargetNode() {
        switch (currentState) {
            case HOLDING -> currentTargetNode = "outer_" + (totalTicks / 40 % 12);
            case STRAFING -> currentTargetNode = "inner_" + (totalTicks / 30 % 8);
            case APPROACH, LANDING -> currentTargetNode = "center_" + (totalTicks / 20 % 4);
            case PERCHING -> currentTargetNode = "fountain";
            case TAKEOFF -> currentTargetNode = "inner_" + (totalTicks / 25 % 8);
            case CHARGING -> {
                Player target = findNearestPlayer();
                if (target != null) {
                    currentTargetNode = findNearestNodeToPlayer(target);
                }
            }
        }
    }
    
    public void startAI() {
        runTaskTimer(plugin, 0L, 1L);
        plugin.getLogger().info("[MATHEMATICAL AI] Mathematical Dragon AI started");
    }
    
    public void stopAI() {
        cancel();
        plugin.getLogger().info("[MATHEMATICAL AI] Mathematical Dragon AI stopped");
    }
    
    private void executeCurrentState() {
        switch (currentState) {
            case HOLDING -> executeHolding();
            case STRAFING -> executeStrafing();
            case APPROACH -> executeApproach();
            case LANDING -> executeLanding();
            case PERCHING -> executePerching();
            case TAKEOFF -> executeTakeoff();
            case CHARGING -> executeCharging();
        }
    }

    private void executeHolding() {
        // Mathematical circling using polar coordinates
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CIRCLING);
        bukkitDragon.setTarget(null);

        // Polar coordinate movement - exact implementation from research
        circlingAngle += 0.02; // Slow, stable circling

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 targetPos = new Vec3(
            homeLocation.getX() + outerRadius * Math.cos(circlingAngle),
            homeLocation.getY() + 10,
            homeLocation.getZ() + outerRadius * Math.sin(circlingAngle)
        );

        // Calculate movement vector with mathematical precision
        Vec3 direction = targetPos.subtract(currentPos);
        double distance = direction.length();

        if (distance > 3.0) {
            Vector velocity = new Vector(
                direction.x * 0.1,
                direction.y * 0.08,
                direction.z * 0.1
            );
            bukkitDragon.setVelocity(velocity);
        }
    }

    private void executeStrafing() {
        // Mathematical strafing pattern
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.STRAFING);

        Player target = findNearestPlayer();
        if (target != null) {
            bukkitDragon.setTarget(target);

            // Sinusoidal movement pattern from research
            double x = 70 * Math.cos(totalTicks * 0.05);
            double z = 45 * Math.sin(totalTicks * 0.06);

            Vec3 strafePos = new Vec3(
                homeLocation.getX() + x,
                homeLocation.getY() + 15,
                homeLocation.getZ() + z
            );

            Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
            Vec3 direction = strafePos.subtract(currentPos);

            if (direction.length() > 2.0) {
                Vector velocity = new Vector(
                    direction.x * 0.08,
                    direction.y * 0.06,
                    direction.z * 0.08
                );
                bukkitDragon.setVelocity(velocity);
            }
        }
    }

    private void executeApproach() {
        // Spiral approach pattern with progressive radius reduction
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CIRCLING);

        double progress = Math.min(1.0, stateTicks / 100.0);
        double currentRadius = outerRadius * (1.0 - progress * 0.7);
        double angle = totalTicks * 0.08;

        Vec3 targetPos = new Vec3(
            homeLocation.getX() + currentRadius * Math.cos(angle),
            homeLocation.getY() + 10,
            homeLocation.getZ() + currentRadius * Math.sin(angle)
        );

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 direction = targetPos.subtract(currentPos);

        if (direction.length() > 2.0) {
            Vector velocity = new Vector(
                direction.x * 0.07,
                direction.y * 0.05,
                direction.z * 0.07
            );
            bukkitDragon.setVelocity(velocity);
        }

        // Transition to landing when close enough
        if (currentRadius < 25) {
            transitionToState(DragonState.LANDING);
        }
    }

    private void executeLanding() {
        // Spiral down pattern with controlled descent
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.LAND_ON_PORTAL);

        double radius = Math.max(5, 25 - stateTicks * 0.3);
        double angle = totalTicks * 0.15;

        Vec3 targetPos = new Vec3(
            homeLocation.getX() + radius * Math.cos(angle),
            Math.max(homeLocation.getY() - 5, homeLocation.getY() - stateTicks * 0.2),
            homeLocation.getZ() + radius * Math.sin(angle)
        );

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 direction = targetPos.subtract(currentPos);

        Vector velocity = new Vector(
            direction.x * 0.06,
            direction.y * 0.08,
            direction.z * 0.06
        );
        bukkitDragon.setVelocity(velocity);

        // Transition to perching when low enough
        if (currentPos.y <= homeLocation.getY() + 5) {
            transitionToState(DragonState.PERCHING);
        }
    }

    private void executePerching() {
        // Minimal movement around perch with mathematical precision
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.LAND_ON_PORTAL);

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 homeVec = new Vec3(homeLocation.getX(), homeLocation.getY(), homeLocation.getZ());

        // Small oscillations around fountain
        Vec3 targetPos = homeVec.add(
            3 * Math.sin(totalTicks * 0.1),
            0,
            3 * Math.cos(totalTicks * 0.1)
        );

        Vec3 direction = targetPos.subtract(currentPos);
        if (direction.length() > 1.0) {
            Vector velocity = new Vector(
                direction.x * 0.03,
                0,
                direction.z * 0.03
            );
            bukkitDragon.setVelocity(velocity);
        }

        // Perch for calculated duration then takeoff
        double perchDuration = 60 + mathRandom.nextDouble() * 60; // 3-6 seconds
        if (stateTicks > perchDuration) {
            transitionToState(DragonState.TAKEOFF);
        }
    }

    private void executeTakeoff() {
        // Expanding spiral pattern
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CIRCLING);

        double radius = Math.min(60, 5 + stateTicks * 0.7);
        double angle = totalTicks * 0.1;

        Vec3 targetPos = new Vec3(
            homeLocation.getX() + radius * Math.cos(angle),
            homeLocation.getY() + 10 + stateTicks * 0.2,
            homeLocation.getZ() + radius * Math.sin(angle)
        );

        Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
        Vec3 direction = targetPos.subtract(currentPos);

        Vector velocity = new Vector(
            direction.x * 0.08,
            direction.y * 0.06,
            direction.z * 0.08
        );
        bukkitDragon.setVelocity(velocity);

        // Transition back to holding when high enough
        if (radius >= 55) {
            transitionToState(DragonState.HOLDING);
        }
    }

    private void executeCharging() {
        // Fast direct movement toward player using graph pathfinding
        bukkitDragon.setPhase(org.bukkit.entity.EnderDragon.Phase.CHARGE_PLAYER);

        Player target = findNearestPlayer();
        if (target != null) {
            bukkitDragon.setTarget(target);

            // Use pathfinding node as intermediate target for smoother movement
            Vec3 nodePos = pathfindingNodes.get(currentTargetNode);
            if (nodePos != null) {
                Vec3 currentPos = Vec3.fromLocation(bukkitDragon.getLocation());
                Vec3 direction = nodePos.subtract(currentPos);

                if (direction.length() > 3.0) {
                    Vector velocity = new Vector(
                        direction.x * 0.12, // Faster movement for charging
                        direction.y * 0.08,
                        direction.z * 0.12
                    );
                    bukkitDragon.setVelocity(velocity);
                }
            }
        } else {
            transitionToState(DragonState.HOLDING);
        }

        // Timeout for charging
        if (stateTicks > 40) { // 2 seconds max
            transitionToState(DragonState.HOLDING);
        }
    }

    private Player findNearestPlayer() {
        return bukkitDragon.getWorld().getPlayers().stream()
            .filter(this::isValidTarget)
            .min((p1, p2) -> Double.compare(
                bukkitDragon.getLocation().distance(p1.getLocation()),
                bukkitDragon.getLocation().distance(p2.getLocation())
            ))
            .orElse(null);
    }

    private boolean isValidTarget(Player player) {
        return player != null &&
               !player.isDead() &&
               player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
               player.getGameMode() != org.bukkit.GameMode.SPECTATOR;
    }

    private String findNearestNodeToPlayer(Player player) {
        Vec3 playerPos = new Vec3(player.getLocation().getX(), player.getLocation().getY(), player.getLocation().getZ());
        String nearestNode = "outer_0";
        double nearestDistance = Double.MAX_VALUE;

        for (Map.Entry<String, Vec3> entry : pathfindingNodes.entrySet()) {
            double distance = playerPos.distanceTo(entry.getValue());
            if (distance < nearestDistance) {
                nearestDistance = distance;
                nearestNode = entry.getKey();
            }
        }

        return nearestNode;
    }

    private void logMathematicalState() {
        plugin.getLogger().info("[MATHEMATICAL AI] State: " + currentState +
            ", Ticks: " + stateTicks +
            ", Aggression: " + String.format("%.2f", aggressionLevel) +
            ", Target: " + currentTargetNode +
            ", Phase: " + bukkitDragon.getPhase());
    }

    // Mathematical Random Number Generator - Linear Congruential Generator
    private static class MathematicalRandom {
        private long seed;
        private static final long MULTIPLIER = 0x5DEECE66DL;
        private static final long ADDEND = 0xBL;
        private static final long MASK = (1L << 48) - 1;

        public MathematicalRandom(long seed) {
            this.seed = (seed ^ MULTIPLIER) & MASK;
        }

        public double nextDouble() {
            return (((long)(next(26)) << 27) + next(27)) * 0x1.0p-53;
        }

        private int next(int bits) {
            seed = (seed * MULTIPLIER + ADDEND) & MASK;
            return (int)(seed >>> (48 - bits));
        }
    }

    // Vec3 Helper Class - Mathematical 3D Vector
    private static class Vec3 {
        public final double x, y, z;
        public static final Vec3 ZERO = new Vec3(0, 0, 0);

        public Vec3(double x, double y, double z) {
            this.x = x;
            this.y = y;
            this.z = z;
        }

        public Vec3 add(Vec3 other) {
            return new Vec3(x + other.x, y + other.y, z + other.z);
        }

        public Vec3 add(double dx, double dy, double dz) {
            return new Vec3(x + dx, y + dy, z + dz);
        }

        public Vec3 subtract(Vec3 other) {
            return new Vec3(x - other.x, y - other.y, z - other.z);
        }

        public Vec3 scale(double factor) {
            return new Vec3(x * factor, y * factor, z * factor);
        }

        public Vec3 normalize() {
            double length = length();
            return length > 0 ? scale(1.0 / length) : ZERO;
        }

        public double length() {
            return Math.sqrt(x * x + y * y + z * z);
        }

        public double distanceTo(Vec3 other) {
            return subtract(other).length();
        }

        public static Vec3 fromLocation(Location loc) {
            return new Vec3(loc.getX(), loc.getY(), loc.getZ());
        }
    }

    // Getters for monitoring
    public DragonState getCurrentState() { return currentState; }
    public double getAggressionLevel() { return aggressionLevel; }
    public String getCurrentTargetNode() { return currentTargetNode; }
    public int getStateTicks() { return stateTicks; }
}
