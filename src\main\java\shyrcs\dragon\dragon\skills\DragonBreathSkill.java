package shyrcs.dragon.dragon.skills;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

import java.util.Random;

/**
 * Dragon's Breath skill - Shoots fire projectiles that knock players up
 */
public class DragonBreathSkill extends DragonSkill {
    
    private final Random random = new Random();
    
    public DragonBreathSkill(SoulDragonPlugin plugin, CustomDragon customDragon) {
        super(plugin, customDragon, "breath");
    }
    
    @Override
    public boolean canUse() {
        return customDragon.getCurrentTarget() != null;
    }
    
    @Override
    public void use() {
        Player target = customDragon.getCurrentTarget();
        if (target == null) {
            return;
        }
        
        Location dragonLocation = customDragon.getDragon().getLocation();
        
        // Play sound
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_ENDER_DRAGON_SHOOT, 2.0f, 0.8f);
        
        // Shoot multiple fire projectiles
        int projectileCount = 5 + random.nextInt(3); // 5-7 projectiles
        
        for (int i = 0; i < projectileCount; i++) {
            new BukkitRunnable() {
                @Override
                public void run() {
                    shootFireProjectile(dragonLocation, target.getLocation());
                }
            }.runTaskLater(plugin, i * 5L); // Stagger projectiles
        }
    }
    
    /**
     * Shoot a fire projectile
     */
    private void shootFireProjectile(Location startLocation, Location targetLocation) {
        // Add some randomness to target location
        Location actualTarget = targetLocation.clone().add(
            random.nextDouble() * 6 - 3,
            random.nextDouble() * 2,
            random.nextDouble() * 6 - 3
        );
        
        Vector direction = actualTarget.toVector().subtract(startLocation.toVector()).normalize();
        
        new BukkitRunnable() {
            private Location currentLocation = startLocation.clone();
            private int ticks = 0;
            private final double speed = 1.5;
            
            @Override
            public void run() {
                ticks++;
                
                if (ticks > 100 || currentLocation.getY() < 0) { // Max 5 seconds or hit ground
                    this.cancel();
                    return;
                }
                
                // Move projectile
                currentLocation.add(direction.clone().multiply(speed));
                
                // Particle effects
                currentLocation.getWorld().spawnParticle(
                    Particle.FLAME,
                    currentLocation,
                    3,
                    0.2, 0.2, 0.2,
                    0.05
                );
                
                currentLocation.getWorld().spawnParticle(
                    Particle.SMOKE,
                    currentLocation,
                    1,
                    0.1, 0.1, 0.1,
                    0.02
                );
                
                // Check for collision with ground or entities
                if (currentLocation.getBlock().getType().isSolid() || 
                    checkEntityCollision(currentLocation)) {
                    explode(currentLocation);
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }
    
    /**
     * Check for entity collision
     */
    private boolean checkEntityCollision(Location location) {
        for (Entity entity : location.getWorld().getNearbyEntities(location, 1.5, 1.5, 1.5)) {
            if (entity instanceof LivingEntity && 
                !entity.equals(customDragon.getDragon())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Create explosion at location
     */
    private void explode(Location location) {
        double damage = getDamage();
        double range = getRange();
        int knockupHeight = plugin.getConfig().getInt("dragon.skills.breath.knockup", 20);
        
        // Particle effects
        location.getWorld().spawnParticle(
            Particle.EXPLOSION,
            location,
            3,
            1.0, 1.0, 1.0,
            0.0
        );
        
        location.getWorld().spawnParticle(
            Particle.FLAME,
            location,
            20,
            2.0, 2.0, 2.0,
            0.1
        );
        
        // Sound
        location.getWorld().playSound(location, Sound.ENTITY_GENERIC_EXPLODE, 1.5f, 1.2f);
        
        // Damage and knockup nearby entities
        for (Entity entity : location.getWorld().getNearbyEntities(location, range, range, range)) {
            if (entity instanceof LivingEntity livingEntity && 
                !entity.equals(customDragon.getDragon())) {
                
                double distance = entity.getLocation().distance(location);
                if (distance <= range) {
                    // Calculate damage based on distance
                    double actualDamage = damage * (1.0 - (distance / range));
                    livingEntity.damage(actualDamage);
                    
                    // Knockup effect
                    Vector knockup = new Vector(0, knockupHeight * (1.0 - (distance / range)), 0);
                    entity.setVelocity(entity.getVelocity().add(knockup));
                    
                    // Add fire effect
                    entity.setFireTicks(60); // 3 seconds of fire
                }
            }
        }
    }
    
    @Override
    public int getCooldownSeconds() {
        return plugin.getConfig().getInt("dragon.skills.breath.cooldown", 20);
    }
}
