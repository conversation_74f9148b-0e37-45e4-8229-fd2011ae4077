package shyrcs.dragon.dragon.skills;

import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import shyrcs.dragon.SoulDragonPlugin;
import shyrcs.dragon.dragon.CustomDragon;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * Lightning Strike skill - Strikes random players with unavoidable lightning
 */
public class LightningStrikeSkill extends DragonSkill {
    
    private final Random random = new Random();
    
    public LightningStrikeSkill(SoulDragonPlugin plugin, CustomDragon customDragon) {
        super(plugin, customDragon, "lightning");
    }
    
    @Override
    public boolean canUse() {
        return !getNearbyPlayers().isEmpty();
    }
    
    @Override
    public void use() {
        List<Player> nearbyPlayers = getNearbyPlayers();
        if (nearbyPlayers.isEmpty()) {
            return;
        }
        
        Location dragonLocation = customDragon.getDragon().getLocation();
        
        // Play warning sound
        dragonLocation.getWorld().playSound(dragonLocation, Sound.ENTITY_ENDER_DRAGON_GROWL, 2.0f, 0.5f);
        
        // Strike 1-3 random players
        int strikeCount = Math.min(1 + random.nextInt(3), nearbyPlayers.size());
        
        for (int i = 0; i < strikeCount; i++) {
            Player target = nearbyPlayers.get(random.nextInt(nearbyPlayers.size()));
            nearbyPlayers.remove(target); // Don't strike the same player twice
            
            // Delay each strike slightly
            new BukkitRunnable() {
                @Override
                public void run() {
                    strikeLightning(target);
                }
            }.runTaskLater(plugin, i * 10L + random.nextInt(20)); // Random delay
        }
    }
    
    /**
     * Get nearby players within range
     */
    private List<Player> getNearbyPlayers() {
        List<Player> players = new ArrayList<>();
        Location dragonLocation = customDragon.getDragon().getLocation();
        double range = getRange();
        
        for (Entity entity : dragonLocation.getWorld().getNearbyEntities(dragonLocation, range, range, range)) {
            if (entity instanceof Player player && 
                player.getGameMode() != org.bukkit.GameMode.CREATIVE &&
                player.getGameMode() != org.bukkit.GameMode.SPECTATOR) {
                players.add(player);
            }
        }
        
        return players;
    }
    
    /**
     * Strike lightning at player
     */
    private void strikeLightning(Player target) {
        Location targetLocation = target.getLocation();
        
        // Warning phase - show particles above player
        new BukkitRunnable() {
            private int ticks = 0;
            
            @Override
            public void run() {
                ticks++;
                
                if (ticks > 40) { // 2 seconds warning
                    // Strike lightning
                    performLightningStrike(target);
                    this.cancel();
                    return;
                }
                
                // Warning particles
                Location warningLocation = target.getLocation().add(0, 10, 0);
                warningLocation.getWorld().spawnParticle(
                    Particle.ELECTRIC_SPARK,
                    warningLocation,
                    5,
                    2.0, 1.0, 2.0,
                    0.1
                );
                
                warningLocation.getWorld().spawnParticle(
                    Particle.CLOUD,
                    warningLocation,
                    3,
                    1.0, 0.5, 1.0,
                    0.05
                );
                
                // Warning sound every 20 ticks
                if (ticks % 20 == 0) {
                    target.playSound(target.getLocation(), Sound.ENTITY_CREEPER_PRIMED, 1.0f, 2.0f);
                }
            }
        }.runTaskTimer(plugin, 0L, 1L);
    }
    
    /**
     * Perform the actual lightning strike
     */
    private void performLightningStrike(Player target) {
        Location strikeLocation = target.getLocation();
        double damage = getDamage();
        
        // Create lightning effect
        strikeLocation.getWorld().strikeLightning(strikeLocation);
        
        // Additional particle effects
        strikeLocation.getWorld().spawnParticle(
            Particle.ELECTRIC_SPARK,
            strikeLocation,
            50,
            3.0, 5.0, 3.0,
            0.2
        );
        
        strikeLocation.getWorld().spawnParticle(
            Particle.EXPLOSION,
            strikeLocation,
            5,
            2.0, 2.0, 2.0,
            0.0
        );
        
        // Sound effects
        strikeLocation.getWorld().playSound(strikeLocation, Sound.ENTITY_LIGHTNING_BOLT_THUNDER, 2.0f, 1.0f);
        strikeLocation.getWorld().playSound(strikeLocation, Sound.ENTITY_GENERIC_EXPLODE, 1.5f, 1.5f);
        
        // Damage nearby entities (small area of effect)
        for (Entity entity : strikeLocation.getWorld().getNearbyEntities(strikeLocation, 3.0, 3.0, 3.0)) {
            if (entity instanceof LivingEntity livingEntity && 
                !entity.equals(customDragon.getDragon())) {
                
                double distance = entity.getLocation().distance(strikeLocation);
                double actualDamage = damage * (1.0 - (distance / 3.0));
                
                livingEntity.damage(actualDamage);
                
                // Stun effect (slowness)
                if (entity instanceof Player player) {
                    player.addPotionEffect(
                        new org.bukkit.potion.PotionEffect(
                            org.bukkit.potion.PotionEffectType.SLOWNESS,
                            40, // 2 seconds
                            2   // Level 3
                        )
                    );
                }
            }
        }
        
        // Send message to target
        target.sendMessage("§c⚡ Bạn đã bị sét đánh!");
    }
    
    @Override
    public int getCooldownSeconds() {
        return plugin.getConfig().getInt("dragon.skills.lightning.cooldown", 25);
    }
}
